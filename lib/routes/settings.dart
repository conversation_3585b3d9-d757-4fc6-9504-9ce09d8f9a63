import 'package:get/get.dart';
import 'package:ivent_app/features/settings/pages/about_page.dart';
import 'package:ivent_app/features/settings/pages/blocked_users_page.dart';
import 'package:ivent_app/features/settings/pages/pdf_viewer_page.dart';
import 'package:ivent_app/features/settings/pages/privacy_settings_page.dart';
import 'package:ivent_app/features/settings/pages/security_settings_page.dart';
import 'package:ivent_app/features/settings/pages/settings_page.dart';
import 'package:ivent_app/features/settings/pages/support_page.dart';

class SettingsPages {
  SettingsPages._();

  static const _prefix = '/settings';

  static const settings = '$_prefix';
  static const privacySettings = '$_prefix/privacy';
  static const securitySettings = '$_prefix/security';
  static const support = '$_prefix/support';
  static const about = '$_prefix/about';
  static const blockedUsers = '$_prefix/blockedUsers';
  static const pdfViewer = '$_prefix/pdfViewer';

  static final routes = [
    GetPage(
      name: settings,
      page: () => const SettingsPage(),
    ),
    GetPage(
      name: privacySettings,
      page: () => const PrivacySettingsPage(),
    ),
    GetPage(
      name: securitySettings,
      page: () => const SecuritySettingsPage(),
    ),
    GetPage(
      name: support,
      page: () => const SupportPage(),
    ),
    GetPage(
      name: about,
      page: () => const AboutPage(),
    ),
    GetPage(
      name: blockedUsers,
      page: () => const BlockedUsersPage(),
    ),
    GetPage(
      name: pdfViewer,
      page: () => const PdfViewerPage(),
    ),
  ];
}

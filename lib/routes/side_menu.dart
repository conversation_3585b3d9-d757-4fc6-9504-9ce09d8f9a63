import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_shared_state.dart';
import 'package:ivent_app/features/side_menu/controllers/profile_completion_controller.dart';
import 'package:ivent_app/features/side_menu/pages/profile_completion_page.dart';
import 'package:ivent_app/features/side_menu/pages/profile_level_steps_page.dart';

class SideMenuBindings implements Bindings {
  @override
  void dependencies() {
    if (Get.isRegistered<ProfileCompletionController>()) return;
    final authService = Get.find<AuthService>();
    final profileSharedState = Get.find<ProfileSharedState>(tag: authService.sessionUser!.sessionId);

    Get.lazyPut(() => ProfileCompletionController(authService, profileSharedState), fenix: true);
  }
}

class SideMenuPages {
  SideMenuPages._();

  static const _prefix = '/sideMenu';

  static const levelSteps = '$_prefix/levelSteps';
  static const profileCompletion = '$_prefix/profileCompletion';

  static final routes = [
    GetPage(
      name: levelSteps,
      page: () => const ProfileLevelStepsPage(),
      binding: SideMenuBindings(),
    ),
    GetPage(
      name: profileCompletion,
      page: () => const ProfileCompletionPage(),
      binding: SideMenuBindings(),
    ),
  ];
}

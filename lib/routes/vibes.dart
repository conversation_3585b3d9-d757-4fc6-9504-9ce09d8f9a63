import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/vibes/controllers/single_vibe_controller.dart';
import 'package:ivent_app/features/vibes/controllers/vibe_upload_controller.dart';
import 'package:ivent_app/features/vibes/controllers/vibes_page_controller.dart';
import 'package:ivent_app/features/vibes/controllers/vibes_state_manager.dart';
import 'package:ivent_app/features/vibes/pages/camera_page.dart';
import 'package:ivent_app/features/vibes/pages/gallery_page.dart';
import 'package:ivent_app/features/vibes/pages/single_vibe_page.dart';
import 'package:ivent_app/features/vibes/pages/vibe_upload.dart';
import 'package:ivent_app/features/vibes/pages/vibes_page.dart';

class VibesPages {
  VibesPages._();

  static const _prefix = '/vibes';

  static const singleVibePage = '$_prefix/singleVibePage';
  static const vibeUploadPage = '$_prefix/vibeUploadPage';
  static const vibesPage = '$_prefix/vibesPage';
  static const cameraPage = '$_prefix/cameraPage';
  static const galleryPage = '$_prefix/galleryPage';

  static final routes = [
    GetPage(
      name: singleVibePage,
      page: () => SingleVibePage(vibeId: Get.arguments),
      binding: SinglePageVibesBindings(),
    ),
    GetPage(
      name: vibeUploadPage,
      page: () => const VibeUpload(),
      binding: VibeUploadBindings(),
    ),
    GetPage(
      name: vibesPage,
      page: () => const VibesPage(),
      binding: VibesPageBindings(),
    ),
    GetPage(
      name: cameraPage,
      page: () => const CameraPage(),
      binding: VibeUploadBindings(),
    ),
    GetPage(
      name: galleryPage,
      page: () => const GalleryPage(),
    ),
  ];
}

class SinglePageVibesBindings implements Bindings {
  @override
  void dependencies() {
    if (Get.isRegistered<SinglePageVibeController>(tag: Get.arguments)) return;
    Get.lazyPut(
      () => SinglePageVibeController(Get.find<AuthService>(), Get.arguments, VibesSharedState(Get.arguments)),
      tag: Get.arguments,
      fenix: true,
    );
  }
}

class VibeUploadBindings implements Bindings {
  @override
  void dependencies() {
    if (Get.isRegistered<VibeUploadController>()) return;
    Get.lazyPut(() => VibeUploadController(Get.find<AuthService>(), VibesSharedState('')), fenix: true);
  }
}

class VibesPageBindings implements Bindings {
  @override
  void dependencies() {
    if (Get.isRegistered<VibesPageController>()) return;
    Get.lazyPut(() => VibesPageController(Get.find<AuthService>(), VibesSharedState('')), fenix: true);
  }
}

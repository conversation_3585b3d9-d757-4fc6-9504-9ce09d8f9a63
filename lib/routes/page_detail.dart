import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/pages/controllers/page_detail_controller.dart';
import 'package:ivent_app/features/pages/pages/page_detail_page.dart';

class PageDetailPages {
  PageDetailPages._();

  static const _prefix = '/pageDetail';

  static const pageDetail = '$_prefix';

  static final routes = [
    GetPage(
      name: pageDetail,
      page: () {
        final arguments = Get.arguments as Map<String, dynamic>;
        final pageId = arguments['pageId'] as String;
        final pageName = arguments['pageName'] as String?;

        return PageDetailPage(
          pageId: pageId,
          pageName: pageName,
        );
      },
      binding: PageDetailBindings(),
    ),
  ];
}

class PageDetailBindings implements Bindings {
  @override
  void dependencies() {
    final arguments = Get.arguments as Map<String, dynamic>;
    final pageId = arguments['pageId'] as String;
    if (Get.isRegistered<PageDetailController>(tag: pageId)) return;

    Get.lazyPut(
      () => PageDetailController(Get.find<AuthService>(), pageId),
      tag: pageId,
      fenix: true,
    );
  }
}

import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_shared_state.dart';
import 'package:ivent_app/features/side_menu/controllers/creator_request_controller.dart';
import 'package:ivent_app/features/side_menu/pages/creator_request/creator_request_status.dart';
import 'package:ivent_app/features/side_menu/pages/creator_request/creator_request_step1.dart';
import 'package:ivent_app/features/side_menu/pages/creator_request/creator_request_step2.dart';
import 'package:ivent_app/features/side_menu/pages/creator_request/creator_request_step3.dart';

class CreatorRequestPages {
  CreatorRequestPages._();

  static const _prefix = '/creatorRequest';

  static const step1 = '$_prefix/step1';
  static const step2 = '$_prefix/step2';
  static const step3 = '$_prefix/step3';
  static const status = '$_prefix/status';

  static final routes = [
    GetPage(
      name: step1,
      page: () => const CreatorRequestStep1(),
      binding: CreatorRequestBindings(),
    ),
    GetPage(
      name: step2,
      page: () => const CreatorRequestStep2(),
      binding: CreatorRequestBindings(),
    ),
    GetPage(
      name: step3,
      page: () => const CreatorRequestStep3(),
      binding: CreatorRequestBindings(),
    ),
    GetPage(
      name: status,
      page: () => const CreatorRequestStatus(),
      binding: CreatorRequestBindings(),
    ),
  ];
}

class CreatorRequestBindings implements Bindings {
  @override
  void dependencies() {
    if (Get.isRegistered<CreatorRequestController>()) return;
    Get.lazyPut<CreatorRequestController>(
      () => CreatorRequestController(Get.find<AuthService>(), Get.find<ProfileSharedState>()),
    );
  }
}

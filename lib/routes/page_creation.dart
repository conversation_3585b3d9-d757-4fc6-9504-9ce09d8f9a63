import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/pages/controllers/page_creation_controller.dart';
import 'package:ivent_app/features/pages/controllers/page_creation_state_manager.dart';
import 'package:ivent_app/features/pages/pages/page_creation_step1.dart';
import 'package:ivent_app/features/pages/pages/page_creation_step2.dart';
import 'package:ivent_app/features/pages/pages/page_creation_step3.dart';
import 'package:ivent_app/features/pages/pages/page_creation_step4.dart';

class PageCreationPages {
  PageCreationPages._();

  static const _prefix = '/pageCreation';

  static const step1 = '$_prefix/step1';
  static const step2 = '$_prefix/step2';
  static const step3 = '$_prefix/step3';
  static const step4 = '$_prefix/step4';

  static final routes = [
    GetPage(
      name: step1,
      page: () => const PageCreationStep1(),
      binding: PageCreationBindings(),
    ),
    GetPage(
      name: step2,
      page: () => const PageCreationStep2(),
      binding: PageCreationBindings(),
    ),
    GetPage(
      name: step3,
      page: () => const PageCreationStep3(),
      binding: PageCreationBindings(),
    ),
    GetPage(
      name: step4,
      page: () => const PageCreationStep4(),
      binding: PageCreationBindings(),
    ),
  ];
}

class PageCreationBindings implements Bindings {
  @override
  void dependencies() {
    if (Get.isRegistered<PageCreationController>()) return;
    Get.lazyPut(() => PageCreationController(Get.find<AuthService>(), PageCreationSharedState()), fenix: true);
  }
}

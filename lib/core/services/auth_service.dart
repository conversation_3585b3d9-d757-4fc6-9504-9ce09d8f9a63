import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/cache/cache_manager.dart';
import 'package:ivent_app/core/utils/api_client_interceptor.dart';
import 'package:ivent_app/features/auth/auth_pages.dart';

class AuthService extends GetxService {
  late final ApiClient apiClient;
  late final AuthApi authApi;
  late final CommentsApi commentsApi;
  late final GroupsApi groupsApi;
  late final GroupMembershipsApi groupMembershipsApi;
  late final HobbiesApi hobbiesApi;
  late final HomeApi homeApi;
  late final IventsApi iventsApi;
  late final IventCollabsApi iventCollabsApi;
  late final LocationsApi locationsApi;
  late final MapboxApi mapboxApi;
  late final MemoriesApi memoriesApi;
  late final NotificationsApi notificationsApi;
  late final PagesApi pagesApi;
  late final PageBlacklistsApi pageBlacklistsApi;
  late final PageMembershipsApi pageMembershipsApi;
  late final SquadMembershipsApi squadMembershipsApi;
  late final UniversitiesApi universitiesApi;
  late final UsersApi usersApi;
  late final UserRelationshipsApi userRelationshipsApi;
  late final VibesApi vibesApi;

  final basePath = dotenv.env['BASE_PATH']!;

  final _sessionUser = Rxn<SessionUser>();
  final _hasUpcomingEventToday = false.obs;

  SessionUser? get sessionUser => _sessionUser.value;
  bool get hasUpcomingEventToday => _hasUpcomingEventToday.value;
  bool get isLoggedIn => _sessionUser.value != null;

  Future<void> login(SessionUser userData) async {
    _sessionUser.value = userData;
    SharedPrefs.setUserToCache(userData);
    apiClient.defaultHeaderMap['Authorization'] = 'Bearer ${userData.token}';
  }

  Future<void> logout() async {
    _sessionUser.value = null;
    SharedPrefs.deleteUserFromCache();
    apiClient.defaultHeaderMap.remove('Authorization');

    try {
      Get.reset();
      debugPrint('✅ [AuthService] All controllers cleared');
    } catch (e) {
      debugPrint('⚠️ [AuthService] Error clearing controllers: $e');
    }

    Get.toNamed(AuthPages.onboarding);
  }

  Future<void> deleteAccount() async {
    if (!isLoggedIn) return;

    try {
      await usersApi.deleteByUserId(_sessionUser.value!.sessionId);
      logout();
    } catch (e) {
      debugPrint('❌ [AuthService] Account deletion failed: $e');
    }
  }

  Future<AuthService> init() async {
    _initializeApiClients();

    debugPrint('🔄 [AuthService] Initializing...');
    final sessionUser = await SharedPrefs.getUserFromCache();
    await checkUpcomingEvents();

    if (sessionUser != null) {
      _sessionUser.value = sessionUser;
      apiClient.defaultHeaderMap['Authorization'] = 'Bearer ${sessionUser.token}';
      debugPrint(
          '✅ [AuthService] User loaded from cache: ${sessionUser.sessionFullname} (${sessionUser.sessionUsername})');
    } else {
      debugPrint('⚠️ [AuthService] No user found in cache');
    }

    return this;
  }

  void _initializeApiClients() {
    apiClient = ApiClient(basePath: basePath);

    // Setup DIO pretty logger for iOS debug mode
    apiClient.setupDioClient(baseUrl: basePath);
    debugPrint('🔧 [AuthService] API Client initialized with DIO pretty logger');

    // Also configure the global defaultApiClient to use the same base path and DIO
    defaultApiClient = ApiClient(basePath: basePath);
    defaultApiClient.setupDioClient(baseUrl: basePath);
    debugPrint('🔧 [AuthService] Default API Client configured with DIO');

    authApi = AuthApi(apiClient);
    commentsApi = CommentsApi(apiClient);
    groupsApi = GroupsApi(apiClient);
    groupMembershipsApi = GroupMembershipsApi(apiClient);
    hobbiesApi = HobbiesApi(apiClient);
    homeApi = HomeApi(apiClient);
    iventsApi = IventsApi(apiClient);
    iventCollabsApi = IventCollabsApi(apiClient);
    locationsApi = LocationsApi(apiClient);
    mapboxApi = MapboxApi(apiClient);
    memoriesApi = MemoriesApi(apiClient);
    notificationsApi = NotificationsApi(apiClient);
    pagesApi = PagesApi(apiClient);
    pageBlacklistsApi = PageBlacklistsApi(apiClient);
    pageMembershipsApi = PageMembershipsApi(apiClient);
    squadMembershipsApi = SquadMembershipsApi(apiClient);
    universitiesApi = UniversitiesApi(apiClient);
    usersApi = UsersApi(apiClient);
    userRelationshipsApi = UserRelationshipsApi(apiClient);
    vibesApi = VibesApi(apiClient);
  }

  bool isTokenExpired() {
    if (_sessionUser.value == null || _sessionUser.value?.token == null) {
      return true;
    }
    try {
      final token = _sessionUser.value!.token;
      final expiration = _getTokenExpiration(token);
      return DateTime.now().isAfter(expiration);
    } catch (e) {
      debugPrint('❌ [AuthService] Error checking token expiration: $e');
      return true; // Consider expired on error
    }
  }

  DateTime _getTokenExpiration(String token) {
    // If using JWT:
    final parts = token.split('.');
    if (parts.length != 3) {
      throw Exception('Invalid token format');
    }

    final payload = parts[1];
    final normalized = base64Url.normalize(payload);
    final decoded = utf8.decode(base64Url.decode(normalized));
    final Map<String, dynamic> data = json.decode(decoded);

    // JWT standard expiration claim is 'exp' in seconds since epoch
    if (data.containsKey('exp')) {
      return DateTime.fromMillisecondsSinceEpoch(data['exp'] * 1000);
    }

    throw Exception('Token does not contain expiration');
  }

  Future<void> checkUpcomingEvents() async {}
}

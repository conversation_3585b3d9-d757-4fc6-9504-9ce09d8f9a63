import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';

class IaCircleAvatar extends StatelessWidget {
  final String? imageUrl;
  final double radius;

  const IaCircleAvatar({
    super.key,
    required this.imageUrl,
    required this.radius,
  });

  @override
  Widget build(BuildContext context) {
    return CircleAvatar(
      radius: radius,
      backgroundColor: AppColors.mediumGrey,
      backgroundImage: imageUrl != null ? NetworkImage(imageUrl!) : null,
      child: imageUrl == null ? const Icon(Icons.image, color: AppColors.white) : null,
    );
  }
}

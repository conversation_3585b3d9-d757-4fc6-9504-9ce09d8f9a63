import 'dart:async';

import 'package:flutter/material.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';

class BaseSearchBarController {
  final Function([String? query]) onSearch;
  final InitialSearchBehavior initialSearchBehavior;
  final int debounceDuration;

  BaseSearchBarController(
    this.onSearch,
    this.initialSearchBehavior, {
    this.debounceDuration = 500,
  });

  Timer? _debounceTimer;

  final _textController = TextEditingController();

  TextEditingController get textController => _textController;

  String get text => _textController.text;
  set text(String value) => _textController.text = value;

  Future<void> initialize() async {
    setupSearchListener();
    await initialSearch();
  }

  Future<void> close() async {
    _debounceTimer?.cancel();
    textController.dispose();
  }

  void setupSearchListener() {
    textController.addListener(() {
      _debounceTimer?.cancel();
      _debounceTimer = Timer(Duration(milliseconds: debounceDuration), () async {
        final textInput = textController.text.trim();
        if (textInput.isNotEmpty) {
          await onSearch(textInput);
        } else {
          clearSearch();
        }
      });
    });
  }

  Future<void> initialSearch() async {
    if (initialSearchBehavior == InitialSearchBehavior.loaded) {
      await onSearch(null);
    }
  }

  Future<void> clearSearch() async {
    textController.clear();
    await initialSearch();
  }
}

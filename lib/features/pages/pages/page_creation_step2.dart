import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/pages/controllers/page_creation_controller.dart';
import 'package:ivent_app/routes/page_creation.dart';

class PageCreationStep2 extends StatefulWidget {
  const PageCreationStep2({Key? key}) : super(key: key);

  @override
  State<PageCreationStep2> createState() => _PageCreationStep2State();
}

class _PageCreationStep2State extends State<PageCreationStep2> {
  late final PageCreationController _controller;
  final TextEditingController _locationSearchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _controller = Get.find<PageCreationController>();
    _controller.loadLatestLocations();
  }

  @override
  void dispose() {
    _locationSearchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      title: 'Sayfa Oluştur',
      body: Padding(
        padding: const EdgeInsets.all(AppDimensions.padding20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Progress indicator
            _buildProgressIndicator(),
            const SizedBox(height: AppDimensions.padding32),

            // Content
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Description section
                    _buildDescriptionSection(),
                    const SizedBox(height: AppDimensions.padding24),

                    // Website URL section
                    _buildWebsiteSection(),
                    const SizedBox(height: AppDimensions.padding24),

                    // Location section
                    _buildLocationSection(),
                    const SizedBox(height: AppDimensions.padding24),

                    // Education and membership toggles
                    _buildSettingsSection(),
                  ],
                ),
              ),
            ),

            // Navigation buttons
            _buildNavigationButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Row(
      children: [
        _buildProgressDot(0, true),
        _buildProgressLine(true),
        _buildProgressDot(1, true),
        _buildProgressLine(false),
        _buildProgressDot(2, false),
      ],
    );
  }

  Widget _buildProgressDot(int step, bool isActive) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: isActive ? AppColors.primary : AppColors.lightGrey,
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Text(
          '${step + 1}',
          style: AppTextStyles.size14Bold.copyWith(
            color: isActive ? AppColors.white : AppColors.textSecondary,
          ),
        ),
      ),
    );
  }

  Widget _buildProgressLine(bool isActive) {
    return Expanded(
      child: Container(
        height: 2,
        color: isActive ? AppColors.primary : AppColors.lightGrey,
      ),
    );
  }

  Widget _buildDescriptionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Açıklama',
          style: AppTextStyles.size16Bold,
        ),
        const SizedBox(height: AppDimensions.padding8),
        Text(
          'Sayfanızı tanıtan kısa bir açıklama yazın (opsiyonel)',
          style: AppTextStyles.size14Regular.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppDimensions.padding16),
        TextField(
          controller: _controller.descriptionController,
          maxLines: 3,
          maxLength: 200,
          decoration: InputDecoration(
            hintText: 'Örn: İTÜ Bilgisayar Mühendisliği öğrencilerinin buluşma noktası',
            hintStyle: AppTextStyles.size14Regular.copyWith(
              color: AppColors.textSecondary,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: AppColors.lightGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: AppColors.primary, width: 2),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: AppColors.lightGrey),
            ),
            contentPadding: const EdgeInsets.all(AppDimensions.padding16),
          ),
          style: AppTextStyles.size14Regular,
        ),
      ],
    );
  }

  Widget _buildWebsiteSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Web Sitesi / Sosyal Medya',
          style: AppTextStyles.size16Bold,
        ),
        const SizedBox(height: AppDimensions.padding8),
        Text(
          'Sayfanızın web sitesi veya sosyal medya linkini ekleyin (opsiyonel)',
          style: AppTextStyles.size14Regular.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppDimensions.padding16),
        TextField(
          controller: _controller.websiteUrlController,
          decoration: InputDecoration(
            hintText: 'https://example.com veya @instagram_username',
            hintStyle: AppTextStyles.size14Regular.copyWith(
              color: AppColors.textSecondary,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: AppColors.lightGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: AppColors.primary, width: 2),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: AppColors.lightGrey),
            ),
            contentPadding: const EdgeInsets.all(AppDimensions.padding16),
          ),
          style: AppTextStyles.size14Regular,
        ),
      ],
    );
  }

  Widget _buildLocationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Konum',
              style: AppTextStyles.size16Bold,
            ),
            Text(
              ' *',
              style: AppTextStyles.size16Bold.copyWith(color: AppColors.error),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.padding8),
        Text(
          'Sayfanızın konumunu seçin',
          style: AppTextStyles.size14Regular.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppDimensions.padding16),

        // Location search field
        TextField(
          controller: _locationSearchController,
          onChanged: (value) {
            if (value.isNotEmpty) {
              _controller.searchLocations(value);
            } else {
              _controller.searchSuggestions.clear();
            }
          },
          decoration: InputDecoration(
            hintText: 'Konum ara...',
            hintStyle: AppTextStyles.size14Regular.copyWith(
              color: AppColors.textSecondary,
            ),
            prefixIcon: const Icon(Icons.search, color: AppColors.textSecondary),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: AppColors.lightGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: AppColors.primary, width: 2),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: AppColors.lightGrey),
            ),
            contentPadding: const EdgeInsets.all(AppDimensions.padding16),
          ),
          style: AppTextStyles.size14Regular,
        ),
        const SizedBox(height: AppDimensions.padding16),

        // Selected location display
        Obx(() {
          if (_controller.selectedLocation != null) {
            return Container(
              padding: const EdgeInsets.all(AppDimensions.padding16),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(color: AppColors.primary.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.location_on, color: AppColors.primary),
                  const SizedBox(width: AppDimensions.padding8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _controller.selectedLocation!.locationName,
                          style: AppTextStyles.size14Bold,
                        ),
                        Text(
                          _controller.selectedLocation!.openAddress,
                          style: AppTextStyles.size12Regular.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, color: AppColors.textSecondary),
                    onPressed: () {
                      _controller.selectedLocation = null;
                      _locationSearchController.clear();
                    },
                  ),
                ],
              ),
            );
          }
          return const SizedBox.shrink();
        }),

        // Location search suggestions (Mapbox style)
        Obx(() {
          if (_controller.isSearchingLocations) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(AppDimensions.padding16),
                child: CircularProgressIndicator(),
              ),
            );
          }

          if (_controller.searchSuggestions.isEmpty && _locationSearchController.text.isNotEmpty) {
            return Container(
              padding: const EdgeInsets.all(AppDimensions.padding16),
              child: Text(
                'Konum bulunamadı',
                style: AppTextStyles.size14Regular.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            );
          }

          if (_controller.searchSuggestions.isNotEmpty) {
            return Container(
              constraints: const BoxConstraints(maxHeight: 200),
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _controller.searchSuggestions.length,
                itemBuilder: (context, index) {
                  final suggestion = _controller.searchSuggestions[index];
                  return ListTile(
                    leading: const Icon(Icons.location_on, color: AppColors.primary),
                    title: Text(
                      suggestion.name,
                      style: AppTextStyles.size14Medium,
                    ),
                    subtitle: Text(
                      suggestion.fullAddress ?? suggestion.placeFormatted,
                      style: AppTextStyles.size12Regular.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    onTap: () {
                      _controller.selectLocationFromSuggestion(suggestion);
                      _locationSearchController.text = suggestion.name;
                    },
                  );
                },
              ),
            );
          }

          return const SizedBox.shrink();
        }),
      ],
    );
  }

  Widget _buildSettingsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sayfa Ayarları',
          style: AppTextStyles.size16Bold,
        ),
        const SizedBox(height: AppDimensions.padding16),

        // Education toggle with tap handler
        Obx(() => GestureDetector(
              onTap: () => _showUniversityInfoDialog(),
              child: Container(
                padding: const EdgeInsets.all(AppDimensions.padding16),
                decoration: BoxDecoration(
                  color: AppColors.lightGrey.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: _controller.isEdu ? Border.all(color: AppColors.primary, width: 2) : null,
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                'Üniversite Topluluğu',
                                style: AppTextStyles.size14Bold,
                              ),
                              const SizedBox(width: AppDimensions.padding8),
                              const Icon(
                                Icons.info_outline,
                                size: 16,
                                color: AppColors.primary,
                              ),
                            ],
                          ),
                          const SizedBox(height: AppDimensions.padding4),
                          Text(
                            'Bu sayfa bir üniversite topluluğu mu?',
                            style: AppTextStyles.size12Regular.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Switch(
                      value: _controller.isEdu,
                      onChanged: (value) => _controller.isEdu = value,
                      activeColor: AppColors.primary,
                    ),
                  ],
                ),
              ),
            )),

        const SizedBox(height: AppDimensions.padding16),

        // Membership toggle with tap handler
        Obx(() => GestureDetector(
              onTap: () => _showMembershipInfoDialog(),
              child: Container(
                padding: const EdgeInsets.all(AppDimensions.padding16),
                decoration: BoxDecoration(
                  color: AppColors.lightGrey.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: _controller.haveMembership ? Border.all(color: AppColors.primary, width: 2) : null,
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                'Üyelik Sistemi',
                                style: AppTextStyles.size14Bold,
                              ),
                              const SizedBox(width: AppDimensions.padding8),
                              const Icon(
                                Icons.info_outline,
                                size: 16,
                                color: AppColors.primary,
                              ),
                            ],
                          ),
                          const SizedBox(height: AppDimensions.padding4),
                          Text(
                            'Sayfanızda üyelik sistemi olsun mu?',
                            style: AppTextStyles.size12Regular.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Switch(
                      value: _controller.haveMembership,
                      onChanged: (value) => _controller.haveMembership = value,
                      activeColor: AppColors.primary,
                    ),
                  ],
                ),
              ),
            )),
      ],
    );
  }

  void _showUniversityInfoDialog() {
    Get.dialog(
      AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.school, color: AppColors.primary),
            const SizedBox(width: AppDimensions.padding8),
            Text(
              'Üniversite Topluluğu',
              style: AppTextStyles.size16Bold,
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Üniversite öğrencilerine özel bir topluluksa burası tam sana göre! Hemen kulübunu doğrula, arkadaş yerini kaçırma!',
              style: AppTextStyles.size14Regular,
            ),
            const SizedBox(height: AppDimensions.padding16),
            Container(
              padding: const EdgeInsets.all(AppDimensions.padding12),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
              child: Row(
                children: [
                  const Icon(Icons.verified, color: AppColors.primary, size: 20),
                  const SizedBox(width: AppDimensions.padding8),
                  Expanded(
                    child: Text(
                      'Doğrulanmış topluluklar daha fazla görünürlük kazanır',
                      style: AppTextStyles.size12Regular.copyWith(
                        color: AppColors.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              'Kapat',
              style: AppTextStyles.size14Medium.copyWith(color: AppColors.textSecondary),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _controller.isEdu = true;
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
            ),
            child: Text(
              'Öğrenci Topluluğu Onayla',
              style: AppTextStyles.size14Bold.copyWith(color: AppColors.white),
            ),
          ),
        ],
      ),
    );
  }

  void _showMembershipInfoDialog() {
    Get.dialog(
      AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.group, color: AppColors.primary),
            const SizedBox(width: AppDimensions.padding8),
            Text(
              'Üyelik Sistemi',
              style: AppTextStyles.size16Bold,
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Üyelik Sistemi belirlediğiniz kullanıcıların sayfanızda özel olarak paylaştığınız etkinliklere erişimini sağlar. Birlikteliği daima korun!',
              style: AppTextStyles.size14Regular,
            ),
            const SizedBox(height: AppDimensions.padding16),
            Container(
              padding: const EdgeInsets.all(AppDimensions.padding12),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      const Icon(Icons.lock, color: AppColors.primary, size: 20),
                      const SizedBox(width: AppDimensions.padding8),
                      Expanded(
                        child: Text(
                          'Özel etkinlik paylaşımı',
                          style: AppTextStyles.size12Bold.copyWith(
                            color: AppColors.primary,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppDimensions.padding8),
                  Row(
                    children: [
                      const Icon(Icons.admin_panel_settings, color: AppColors.primary, size: 20),
                      const SizedBox(width: AppDimensions.padding8),
                      Expanded(
                        child: Text(
                          'Üye yönetimi ve admin atama',
                          style: AppTextStyles.size12Bold.copyWith(
                            color: AppColors.primary,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              'Kapat',
              style: AppTextStyles.size14Medium.copyWith(color: AppColors.textSecondary),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _controller.haveMembership = true;
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
            ),
            child: Text(
              'Üyelik Sistemini Onayla',
              style: AppTextStyles.size14Bold.copyWith(color: AppColors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Get.back(),
            style: OutlinedButton.styleFrom(
              side: const BorderSide(color: AppColors.primary),
              padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
            child: Text(
              'Geri',
              style: AppTextStyles.size16Bold.copyWith(color: AppColors.primary),
            ),
          ),
        ),
        const SizedBox(width: AppDimensions.padding16),
        Expanded(
          child: Obx(() => ElevatedButton(
                onPressed: _controller.canProceedFromStep(1) ? () => Get.toNamed(PageCreationPages.step3) : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  disabledBackgroundColor: AppColors.lightGrey,
                  padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  ),
                ),
                child: Text(
                  'Devam Et',
                  style: AppTextStyles.size16Bold.copyWith(
                    color: _controller.canProceedFromStep(1) ? AppColors.white : AppColors.textSecondary,
                  ),
                ),
              )),
        ),
      ],
    );
  }
}

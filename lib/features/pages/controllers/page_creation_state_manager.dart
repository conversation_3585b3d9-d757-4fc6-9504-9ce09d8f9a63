import 'package:get/get.dart';
import 'package:ivent_app/core/controllers/shared_state.dart';

/// Shared state manager for page creation feature
///
/// Manages reactive state for page creation process including form data,
/// step management, validation states, and submission status. Provides
/// comprehensive getters, setters, and helper methods for state management.
///
/// Follows the architecture guide's state management patterns with proper
/// reactive variables and helper methods.
class PageCreationSharedState extends SharedState {
  /// Constructor
  PageCreationSharedState();

  // Reactive state variables
  final _currentStep = RxInt(0);
  final _completedSteps = <int>[].obs;
  final _isSubmitting = RxBool(false);
  final _formValidationErrors = <String, String>{}.obs;
  final _lastUpdated = Rx<DateTime?>(null);
  final _isDirty = RxBool(false);

  // Getters and setters with proper reactive patterns

  /// Current step getter
  int get currentStep => _currentStep.value;

  /// Current step setter
  set currentStep(int value) {
    _currentStep.value = value;
    updateTimestamp();
  }

  /// Completed steps getter
  List<int> get completedSteps => _completedSteps.toList();

  /// Completed steps setter
  set completedSteps(List<int> value) => _completedSteps.assignAll(value);

  /// Submission status getter
  bool get isSubmitting => _isSubmitting.value;

  /// Submission status setter
  set isSubmitting(bool value) => _isSubmitting.value = value;

  /// Form validation errors getter
  Map<String, String> get formValidationErrors => Map.from(_formValidationErrors);

  /// Form validation errors setter
  set formValidationErrors(Map<String, String> value) => _formValidationErrors.assignAll(value);

  /// Last updated timestamp getter
  DateTime? get lastUpdated => _lastUpdated.value;

  /// Last updated timestamp setter
  set lastUpdated(DateTime? value) => _lastUpdated.value = value;

  /// Dirty state getter
  bool get isDirty => _isDirty.value;

  /// Dirty state setter
  set isDirty(bool value) => _isDirty.value = value;

  // Helper methods

  /// Checks if a step is completed
  bool isStepCompleted(int step) => _completedSteps.contains(step);

  /// Marks a step as completed
  void markStepCompleted(int step) {
    if (!_completedSteps.contains(step)) {
      _completedSteps.add(step);
      updateTimestamp();
    }
  }

  /// Removes a step from completed list
  void unmarkStepCompleted(int step) {
    _completedSteps.remove(step);
    updateTimestamp();
  }

  /// Advances to the next step
  void nextStep() {
    markStepCompleted(currentStep);
    currentStep = currentStep + 1;
  }

  /// Goes back to the previous step
  void previousStep() {
    if (currentStep > 0) {
      currentStep = currentStep - 1;
    }
  }

  /// Starts submission process
  void startSubmission() {
    isSubmitting = true;
    clearValidationErrors();
  }

  /// Ends submission process
  void endSubmission() {
    isSubmitting = false;
  }

  /// Updates the last updated timestamp to now
  void updateTimestamp() {
    lastUpdated = DateTime.now();
    isDirty = true;
  }

  /// Adds a validation error for a field
  void addValidationError(String field, String error) {
    _formValidationErrors[field] = error;
  }

  /// Removes a validation error for a field
  void removeValidationError(String field) {
    _formValidationErrors.remove(field);
  }

  /// Clears all validation errors
  void clearValidationErrors() {
    _formValidationErrors.clear();
  }

  /// Checks if form has validation errors
  bool get hasValidationErrors => _formValidationErrors.isNotEmpty;

  /// Gets validation error for a specific field
  String? getValidationError(String field) => _formValidationErrors[field];

  /// Resets all form data to initial state
  void resetFormData() {
    currentStep = 0;
    completedSteps = [];
    isSubmitting = false;
    clearValidationErrors();
    lastUpdated = null;
    isDirty = false;
  }

  /// Checks if the form can be submitted
  bool get canSubmit => !isSubmitting && !hasValidationErrors && completedSteps.length >= 3;

  /// Gets the completion percentage
  double get completionPercentage {
    const totalSteps = 4; // Assuming 4 steps in page creation
    return (completedSteps.length / totalSteps).clamp(0.0, 1.0);
  }

  /// Gets a summary of the current state
  Map<String, dynamic> get stateSummary {
    return {
      'currentStep': currentStep,
      'completedSteps': completedSteps,
      'isSubmitting': isSubmitting,
      'hasValidationErrors': hasValidationErrors,
      'validationErrorCount': _formValidationErrors.length,
      'lastUpdated': lastUpdated?.toIso8601String(),
      'isDirty': isDirty,
      'canSubmit': canSubmit,
      'completionPercentage': completionPercentage,
    };
  }
}

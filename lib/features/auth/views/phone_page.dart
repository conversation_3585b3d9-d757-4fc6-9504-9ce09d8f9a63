import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/auth/constants/auth_dimensions.dart';
import 'package:ivent_app/features/auth/constants/strings.dart';
import 'package:ivent_app/features/auth/controllers/auth_phone_controller.dart';
import 'package:ivent_app/features/auth/widgets/auth_info_text_widget.dart';
import 'package:ivent_app/features/auth/widgets/phone_input_widget.dart';

class PhonePage extends GetView<AuthPhoneController> {
  const PhonePage({super.key});

  @override
  Widget build(BuildContext context) {
    return IaScaffold.auth(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Spacer(),
          const AuthInfoTextWidget(text: AuthStrings.iletisimNumGirerek),
          const SizedBox(height: AuthDimensions.sectionSpacing),
          PhoneInputWidget(
            controller: controller.phoneTextController,
            onValidationChanged: controller.handlePhoneValidationChanged,
            onChanged: controller.handlePhoneNumberChanged,
          ),
          const Spacer(),
          IaFloatingActionButton(
            isEnabled: controller.canContinueToCodePage.value,
            text: AuthStrings.devamEt,
            onPressed: controller.goToValidatePhonePage,
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/auth/constants/auth_dimensions.dart';
import 'package:ivent_app/features/auth/constants/strings.dart';
import 'package:ivent_app/features/auth/controllers/auth_validation_controller.dart';
import 'package:ivent_app/features/auth/widgets/auth_info_text_widget.dart';
import 'package:ivent_app/features/auth/widgets/validation_code_widget.dart';

class ValidatePhone extends GetView<AuthValidationController> {
  const ValidatePhone({super.key});

  @override
  Widget build(BuildContext context) {
    return IaScaffold.auth(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Spacer(),
          const AuthInfoTextWidget(text: AuthStrings.koduGirebilirsin),
          const SizedBox(height: AuthDimensions.sectionSpacing),
          ValidationCodeWidget(
            onCompleted: controller.handleValidationCodeChanged,
            onEditingChanged: controller.handleValidationCodeEditingChanged,
            onValidationChanged: controller.handleValidationCodeValidationChanged,
          ),
          const Spacer(),
          Obx(() {
            return IaFloatingActionButton(
              isEnabled: controller.canContinueToNextPage.value,
              isLoading: controller.isLoading(),
              text: AuthStrings.devamEt,
              onPressed: controller.validateUser,
            );
          }),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/composite/buttons/hobby_buttons.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/core/widgets/layout/screens/ia_search_screen.dart';
import 'package:ivent_app/features/auth/constants/auth_dimensions.dart';
import 'package:ivent_app/features/auth/constants/strings.dart';
import 'package:ivent_app/features/auth/controllers/auth_registration_controller.dart';
import 'package:ivent_app/features/auth/widgets/hobby_category_box.dart';
import 'package:ivent_app/shared/domain/entities/hobby.dart';

class RegistrationHobbiesPage extends GetView<AuthRegistrationController> {
  const RegistrationHobbiesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      showBackButton: false,
      title: AuthStrings.ilgiAlani,
      bodyPadding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: AppDimensions.padding20),
          const Text(
            AuthStrings.ilgiAlaniText,
            style: TextStyle(fontSize: 14, color: Colors.grey),
            maxLines: null,
          ),
          Expanded(
            child: IaSearchScreen(
              textController: controller.textController,
              body: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (controller.checkedHobbyIds.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(bottom: AuthDimensions.formElementSpacing),
                      height: AppDimensions.buttonHeightSelectedHobbyTag,
                      child: ListView.separated(
                        scrollDirection: Axis.horizontal,
                        shrinkWrap: true,
                        itemCount: controller.checkedHobbyIds.length,
                        itemBuilder: (context, index) => HobbyButtons.selectedHobbyTag(
                          onTap: () => controller.toggleHobby(controller.checkedHobbyIds[index]),
                          text: Hobby.getHobbyNameFromHobbyId(controller.checkedHobbyIds[index]),
                        ),
                        separatorBuilder: (context, index) =>
                            const SizedBox(width: AuthDimensions.relatedElementSpacing),
                      ),
                    ),
                  Expanded(
                    child: ListView.separated(
                      padding: const EdgeInsets.only(bottom: 100),
                      itemCount: controller.hobbyCategories.length,
                      itemBuilder: (context, index) {
                        return HobbyCategoryBox(
                          mainCategory: controller.hobbyCategories.elementAt(index),
                          hobbyList: controller.hobbyLists.elementAt(index),
                          selectedHobbyIds: controller.checkedHobbyIds,
                          onHobbyToggle: controller.toggleHobby,
                        );
                      },
                      separatorBuilder: (context, index) => const SizedBox(height: AuthDimensions.hobbyCategorySpacing),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: IaFloatingActionButton(
        isEnabled: controller.areHobbiesValid,
        text: AuthStrings.devamEt,
        onPressed: controller.completeRegistration,
      ),
    );
  }
}

import 'package:get/get.dart';
import 'package:ivent_app/features/auth/constants/validation_constants.dart';
import 'package:ivent_app/core/controllers/shared_state.dart';

class AuthSharedState extends SharedState {
  final phoneNumber = ''.obs;
  bool accessToContacts = false;

  String get formattedPhoneNumber {
    if (phoneNumber.value.length != AuthValidationConstants.phoneNumberMaxLength) {
      return '';
    }
    return _formatPhoneNumber(phoneNumber.value);
  }

  bool get isPhoneNumberValid => phoneNumber.value.length == AuthValidationConstants.phoneNumberMaxLength;

  String _formatPhoneNumber(String value) {
    if (value.length != AuthValidationConstants.phoneNumberMaxLength) {
      return value;
    }

    final String areaCode = value.substring(0, 3);
    final String remainingNumber = value.substring(3);
    return '${AuthValidationConstants.turkeyCountryCode}($areaCode)$remainingNumber';
  }
}

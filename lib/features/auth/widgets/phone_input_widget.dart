import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/auth/constants/auth_dimensions.dart';
import 'package:ivent_app/features/auth/constants/strings.dart';
import 'package:ivent_app/features/auth/constants/validation_constants.dart';

class PhoneInputWidget extends StatefulWidget {
  final TextEditingController controller;
  final ValueChanged<bool> onValidationChanged;
  final ValueChanged<String>? onChanged;

  const PhoneInputWidget({
    super.key,
    required this.controller,
    required this.onValidationChanged,
    this.onChanged,
  });

  @override
  State<PhoneInputWidget> createState() => _PhoneInputWidgetState();
}

class _PhoneInputWidgetState extends State<PhoneInputWidget> {
  
  void _handleTextChange(String text) {
    const int maxLength = AuthValidationConstants.phoneNumberMaxLength;
    final bool isValid = text.length == maxLength;

    if (text.length > maxLength) {
      widget.controller.value = widget.controller.value.copyWith(
        text: text.substring(0, maxLength),
        selection: const TextSelection.collapsed(offset: maxLength),
      );
      return;
    }

    widget.onValidationChanged(isValid);
    widget.onChanged?.call(text);
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(AuthStrings.alanCode, style: AppTextStyles.size32BoldTextSecondary),
        const SizedBox(width: AuthDimensions.relatedElementSpacing),
        _buildPhoneNumberField(),
      ],
    );
  }

  Widget _buildPhoneNumberField() {
    return SizedBox(
      width: AuthDimensions.phoneInputWidth,
      child: TextFormField(
        keyboardType: TextInputType.phone,
        textAlign: TextAlign.left,
        inputFormatters: [
          FilteringTextInputFormatter.allow(
            RegExp(AuthValidationConstants.numericOnlyPattern),
          ),
        ],
        controller: widget.controller,
        onChanged: _handleTextChange,
        decoration: InputDecoration(
          hintText: AuthValidationConstants.phoneNumberPlaceholder,
          hintStyle: AppTextStyles.size32BoldTextSecondary,
          border: InputBorder.none,
          focusedBorder: InputBorder.none,
        ),
        style: AppTextStyles.size32Bold,
      ),
    );
  }
}

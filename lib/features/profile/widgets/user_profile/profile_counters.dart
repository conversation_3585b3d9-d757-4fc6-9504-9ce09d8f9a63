import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/profile/controllers/profile_user_info_controller.dart';
import 'package:ivent_app/features/profile/profile_pages.dart';

class ProfileCounters extends StatelessWidget {
  final int count;
  final String text;
  final VoidCallback? onTap;

  const ProfileCounters._({
    required this.count,
    required this.text,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Text(count.toString(), style: AppTextStyles.size16Bold),
          Text(text, style: AppTextStyles.size12MediumTextSecondary),
        ],
      ),
    );
  }

  /// Factory constructor for ivent count display
  static ProfileCounters iventCounter(ProfileUserInfoController controller) {
    final pageContent = controller.userPageInfo.value!;
    return ProfileCounters._(
      count: pageContent.iventCount,
      text: "iVent'ler",
      onTap: () => Get.toNamed(ProfilePages.userIvents),
    );
  }

  /// Factory constructor for friend count display
  static ProfileCounters friendCounter(ProfileUserInfoController controller) {
    final pageContent = controller.userPageInfo.value!;
    return ProfileCounters._(
      count: pageContent.friendCount,
      text: 'Arkadaşlar',
      onTap: () => Get.toNamed(ProfilePages.userFriends),
    );
  }

  /// Factory constructor for follower count display
  static ProfileCounters followerCounter(ProfileUserInfoController controller) {
    final pageContent = controller.userPageInfo.value!;
    return ProfileCounters._(
      count: pageContent.followerCount,
      text: 'Takipçiler',
      onTap: () => Get.toNamed(ProfilePages.userFollowers),
    );
  }
}

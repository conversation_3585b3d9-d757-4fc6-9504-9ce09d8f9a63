import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_icon_button.dart';
import 'package:ivent_app/routes/settings.dart';

class ProfileHeader extends StatelessWidget {
  const ProfileHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return const Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _HeaderLeading(),
            _HeaderDetails(),
            _HeaderTrailing(),
          ],
        ),
        Divider(),
      ],
    );
  }
}

class _HeaderLeading extends StatelessWidget {
  const _HeaderLeading();

  @override
  Widget build(BuildContext context) {
    return IaIconButton(
      onPressed: Scaffold.of(context).openDrawer,
      iconPath: AppAssets.hamburgerMD,
      iconSize: AppDimensions.defaultIconButtonSize,
      iconColor: AppColors.darkGrey,
    );
  }
}

class _HeaderDetails extends GetView<AuthService> {
  const _HeaderDetails();

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(controller.sessionUser!.sessionFullname, style: AppTextStyles.size16Bold),
        Text('@${controller.sessionUser!.sessionUsername}', style: AppTextStyles.size12MediumTextSecondary),
      ],
    );
  }
}

class _HeaderTrailing extends StatelessWidget {
  const _HeaderTrailing();

  @override
  Widget build(BuildContext context) {
    return IaIconButton(
      onPressed: () => Get.toNamed(SettingsPages.settings),
      iconPath: AppAssets.settings,
      iconSize: AppDimensions.defaultIconButtonSize,
      iconColor: AppColors.darkGrey,
    );
  }
}

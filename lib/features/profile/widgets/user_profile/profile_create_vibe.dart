import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_circular_button.dart';
import 'package:ivent_app/features/ivent_create/ivent_create_pages.dart';

class ProfileCreateVibe extends StatelessWidget {
  const ProfileCreateVibe({super.key});

  @override
  Widget build(BuildContext context) {
    return IaCircularButton(
      onPressed: () => Get.toNamed(IventCreatePages.categorySelection),
      buttonSize: AppDimensions.buttonSizecreateVibe,
      backgroundColor: AppColors.secondary,
      iconPath: AppAssets.addPlus,
    );
  }
}

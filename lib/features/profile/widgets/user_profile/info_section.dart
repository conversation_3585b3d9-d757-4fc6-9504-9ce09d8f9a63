import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/constants/enums/user_type_enum.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_circular_button.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_rounded_button.dart';
import 'package:ivent_app/features/profile/controllers/profile_user_info_controller.dart';
import 'package:ivent_app/features/profile/profile_pages.dart';
import 'package:ivent_app/features/profile/widgets/user_profile/profile_buttons.dart';
import 'package:ivent_app/features/profile/widgets/user_profile/profile_counters.dart';

class ProfileInfoSection extends StatelessWidget {
  const ProfileInfoSection({super.key});

  @override
  Widget build(BuildContext context) {
    return const Column(
      children: [
        _InfoHeader(),
        _InfoButtons(),
        _InfoHobbies(),
      ],
    );
  }
}

class _InfoHeader extends GetView<ProfileUserInfoController> {
  const _InfoHeader();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ProfileCounters.iventCounter(controller),
          SizedBox(width: Get.width * 0.13),
          CircleAvatar(
            radius: Get.width * 0.084,
            backgroundImage: controller.userPageInfo.value!.avatarUrl != null
                ? NetworkImage(controller.userPageInfo.value!.avatarUrl!)
                : null,
          ),
          SizedBox(width: Get.width * 0.13),
          controller.userPageInfo.value!.userRole == UserTypeEnum.CREATOR
              ? ProfileCounters.followerCounter(controller)
              : ProfileCounters.friendCounter(controller),
        ],
      ),
    );
  }
}

class _InfoButtons extends GetView<ProfileUserInfoController> {
  const _InfoButtons();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      child: Row(
        spacing: AppDimensions.padding12,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: _buildButtonLayout().map((buttonLayout) => Expanded(child: buttonLayout)).toList(),
      ),
    );
  }

  List<Widget> _buildButtonLayout() {
    final pageContent = controller.userPageInfo.value!;
    final isFirstPerson = pageContent.isFirstPerson;
    final isCreator = pageContent.userRole == UserTypeEnum.CREATOR;

    if (!isFirstPerson && isCreator) {
      return [
        Obx(() => ProfileButtons.profileFollow(
              onTap: controller.toggleFollowing,
              isFollowing: controller.isFollowing.value,
            )),
        Obx(() => ProfileButtons.profileAddFriend(
              onTap: controller.toggleFriendship,
              relationshipStatus: controller.relationshipStatus.value,
            )),
      ];
    } else if (!isFirstPerson && !isCreator) {
      return [
        Obx(() => ProfileButtons.profileAddFriend(
              onTap: controller.toggleFriendship,
              relationshipStatus: controller.relationshipStatus.value,
            )),
      ];
    } else {
      return [
        ProfileButtons.profileFollowings(onTap: () => Get.toNamed(ProfilePages.userFollowings)),
        ProfileButtons.profileFavorites(onTap: () => Get.toNamed(ProfilePages.userFavorites)),
      ];
    }
  }
}

class _InfoHobbies extends GetView<ProfileUserInfoController> {
  const _InfoHobbies();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: AppDimensions.padding20),
      child: Row(
        children: [
          if (controller.userPageInfo.value!.isFirstPerson)
            const Padding(
              padding: EdgeInsets.only(left: AppDimensions.padding20, right: AppDimensions.padding8),
              child: _EditHobbyButton(),
            ),
          Expanded(
            child: SizedBox(
              height: AppDimensions.buttonHeightProfileTag,
              child: ListView.separated(
                padding: const EdgeInsets.only(right: AppDimensions.padding20),
                scrollDirection: Axis.horizontal,
                itemCount: controller.userPageInfo.value!.hobbies.length,
                itemBuilder: (context, index) => _HobbyTag(
                  onTap: () {},
                  text: controller.userPageInfo.value!.hobbies[index],
                ),
                separatorBuilder: (context, index) => const SizedBox(width: AppDimensions.padding8),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _EditHobbyButton extends StatelessWidget {
  const _EditHobbyButton();

  @override
  Widget build(BuildContext context) {
    return IaCircularButton(
      key: key,
      buttonSize: AppDimensions.buttonSizeProfileTagEdit,
      backgroundColor: AppColors.primary,
      iconPath: AppAssets.editPencil02,
      iconSize: AppDimensions.buttonSizeProfileTagEdit,
    );
  }
}

class _HobbyTag extends StatelessWidget {
  final VoidCallback? onTap;
  final String text;

  const _HobbyTag({
    this.onTap,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return IaRoundedButton(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding12),
      height: AppDimensions.buttonHeightProfileTag,
      roundness: AppDimensions.buttonRadiusL,
      onTap: onTap,
      color: AppColors.lightGrey,
      text: text,
      textStyle: AppTextStyles.size12Medium,
    );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/profile/controllers/profile_followers_controller.dart';
import 'package:ivent_app/features/profile/profile_pages.dart';

class UserFollowers extends GetView<ProfileFollowersController> {
  final String userId;

  const UserFollowers({Key? key, required this.userId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IaScaffold.search(
      title: 'Takipçiler',
      textEditingController: controller.textController,
      body: Obx(() {
        final followersResult = controller.followersResult;
        return IaSearchPlaceholder(
          entityName: 'Takipçi',
          isSearching: controller.isSearching,
          isResultsEmpty: controller.isResultsEmpty,
          isQueryEmpty: controller.isQueryEmpty,
          initialSearchBehavior: InitialSearchBehavior.loaded,
          builder: (context) => ListView.separated(
            padding: const EdgeInsets.only(bottom: 100),
            itemCount: followersResult!.followerCount,
            itemBuilder: (context, index) {
              final follower = followersResult.followers[index];
              return _SearchResult(follower: follower);
            },
            separatorBuilder: IaListTile.separatorBuilder20,
          ),
        );
      }),
    );
  }
}

class _SearchResult extends StatelessWidget {
  const _SearchResult({
    required this.follower,
  });

  final UserListItemWithRelationshipStatus follower;

  @override
  Widget build(BuildContext context) {
    return IaListTile.withImageUrl(
      avatarUrl: follower.avatarUrl,
      title: '@${follower.username}',
      subtitle: follower.university,
      onTap: () => Get.toNamed(ProfilePages.userProfile, arguments: follower.userId),
      trailing: SharedButtons.moreVertical(),
    );
  }
}

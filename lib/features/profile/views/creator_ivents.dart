import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/constants/enums/account_enum.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/ivent_detail/ivent_detail_pages.dart';
import 'package:ivent_app/features/profile/controllers/profile_created_ivents_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_ivents_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_joined_ivents_controller.dart';

class CreatorIvents extends StatefulWidget {
  final String userId;

  const CreatorIvents({Key? key, required this.userId}) : super(key: key);

  @override
  State<CreatorIvents> createState() => _CreatorIventsState();
}

class _CreatorIventsState extends State<CreatorIvents> with SingleTickerProviderStateMixin {
  late final ProfileIventsController _controller;
  late final TabController _tabController;

  ProfileCreatedIventsController get _createdIventsController => _controller.createdIventsController;
  ProfileJoinedIventsController get _joinedIventsController => _controller.joinedIventsController;

  @override
  void initState() {
    super.initState();
    _controller = Get.find(tag: widget.userId);
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: IaScaffold.basic(
        appBar: IaTopBar(
          height: 30,
          alignment: Alignment.center,
          child: Text('iVent\'ler', style: AppTextStyles.size16Bold),
          leading: SharedButtons.backButton(),
          divider: _buildTabButtons(),
        ),
        body: Obx(() {
          return TabBarView(
            controller: _tabController,
            children: [
              _joinedIventsController.iventsResult,
              _createdIventsController.iventsResult,
            ].asMap().entries.map((entry) {
              final iventsResult = entry.value;
              return IaSearchScreen(
                margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
                textController:
                    entry.key == 0 ? _joinedIventsController.textController : _createdIventsController.textController,
                body: IaSearchPlaceholder(
                  entityName: 'iVent',
                  isSearching:
                      entry.key == 0 ? _joinedIventsController.isSearching : _createdIventsController.isSearching,
                  isResultsEmpty: iventsResult?.ivents.isEmpty ?? true,
                  isQueryEmpty:
                      entry.key == 0 ? _joinedIventsController.isQueryEmpty : _createdIventsController.isQueryEmpty,
                  initialSearchBehavior: InitialSearchBehavior.loaded,
                  builder: (context) {
                    return ListView.separated(
                      padding: const EdgeInsets.only(bottom: 100),
                      itemCount: iventsResult!.ivents.length,
                      itemBuilder: (context, index) {
                        final ivent = iventsResult.ivents[index];
                        return IaIventTile(
                          imageUrl: ivent.thumbnailUrl,
                          iventName: ivent.iventName,
                          locationName: ivent.locationName,
                          date: ivent.dates.map((e) => DateTime.parse(e)).toList(),
                          memberAvatarUrls: ivent.memberAvatarUrls,
                          memberCount: ivent.memberCount,
                          memberNames: ivent.memberFirstnames,
                          isOrganizerUser: ivent.creatorType != AccountEnum.DISTRIBUTOR,
                          organizerName: ivent.creatorUsername,
                          organizerAvatarUrl: ivent.creatorImageUrl,
                          viewType: ivent.viewType,
                          onTap: () => Get.toNamed(IventDetailPages.iventDetail, arguments: ivent.iventId),
                        );
                      },
                      separatorBuilder: IaListTile.separatorBuilder20,
                    );
                  },
                ),
              );
            }).toList(),
          );
        }),
      ),
    );
  }

  Widget _buildTabButtons() {
    return Container(
      height: 30,
      child: TabBar(
        controller: _tabController,
        dividerHeight: 4,
        indicator: UnderlineTabIndicator(
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          borderSide: const BorderSide(width: 4, color: AppColors.primary),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: AppColors.lightGrey,
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        tabs: [
          _buildTabButton('Katıldıkların', _tabController.index == 0),
          _buildTabButton('Oluşturdukların', _tabController.index == 1),
        ],
      ),
    );
  }

  Tab _buildTabButton(String text, bool isActive) {
    return Tab(
      child: Text(
        text,
        style: isActive ? AppTextStyles.size14Bold : AppTextStyles.size14MediumTextSecondary,
      ),
    );
  }
}

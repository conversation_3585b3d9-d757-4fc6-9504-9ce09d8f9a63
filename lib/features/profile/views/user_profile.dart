import 'package:flutter/material.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/profile/widgets/user_profile/header.dart';
import 'package:ivent_app/features/profile/widgets/user_profile/info_section.dart';
import 'package:ivent_app/features/profile/widgets/user_profile/tabs.dart';

class UserProfile extends StatelessWidget {
  final String userId;

  const UserProfile({Key? key, required this.userId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IaScaffold.empty(
      body: Column(
        children: [
          const ProfileHeader(),
          DefaultTabController(
            length: 2,
            child: NestedScrollView(
              headerSliverBuilder: (context, value) {
                return [
                  const SliverToBoxAdapter(child: ProfileInfoSection()),
                ];
              },
              body: const ProfileTabs(),
            ),
          ),
        ],
      ),
    );
  }
}

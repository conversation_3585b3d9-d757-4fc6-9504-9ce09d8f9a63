import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/enums/account_enum.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_ivent_tile.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/ivent_detail/ivent_detail_pages.dart';
import 'package:ivent_app/features/profile/controllers/profile_ivents_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_joined_ivents_controller.dart';

class UserIvents extends GetView<ProfileIventsController> {
  final String userId;

  const UserIvents({Key? key, required this.userId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ProfileJoinedIventsController joinedIventsController = controller.joinedIventsController;

    return IaScaffold.search(
      title: "iVent'ler",
      textEditingController: joinedIventsController.textController,
      searchBarLabelText: "Tüm iVent'lerden ara",
      body: Obx(() {
        final joinedIventsResult = joinedIventsController.iventsResult;
        return IaSearchPlaceholder(
          entityName: 'iVent',
          isSearching: joinedIventsController.isSearching,
          isResultsEmpty: joinedIventsController.isResultsEmpty,
          isQueryEmpty: joinedIventsController.isQueryEmpty,
          initialSearchBehavior: InitialSearchBehavior.loaded,
          builder: (context) => ListView.separated(
            padding: const EdgeInsets.only(bottom: 100),
            itemCount: joinedIventsResult!.iventCount,
            itemBuilder: (context, index) {
              final ivent = joinedIventsResult.ivents[index];
              return _SearchResult(ivent: ivent);
            },
            separatorBuilder: IaListTile.separatorBuilder20,
          ),
        );
      }),
    );
  }
}

class _SearchResult extends StatelessWidget {
  const _SearchResult({
    required this.ivent,
  });

  final IventListItem ivent;

  @override
  Widget build(BuildContext context) {
    return IaIventTile(
      imageUrl: ivent.thumbnailUrl,
      iventName: ivent.iventName,
      locationName: ivent.locationName,
      date: ivent.dates.map((e) => DateTime.parse(e)).toList(),
      memberAvatarUrls: ivent.memberAvatarUrls,
      memberCount: ivent.memberCount,
      memberNames: ivent.memberFirstnames,
      isOrganizerUser: ivent.creatorType != AccountEnum.DISTRIBUTOR,
      organizerName: ivent.creatorUsername,
      organizerAvatarUrl: ivent.creatorImageUrl,
      viewType: ivent.viewType,
      onTap: () => Get.toNamed(IventDetailPages.iventDetail, arguments: ivent.iventId),
    );
  }
}

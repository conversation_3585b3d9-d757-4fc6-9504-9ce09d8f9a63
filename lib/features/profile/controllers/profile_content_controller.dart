import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_shared_state.dart';

class ProfileContentController extends BaseController<ProfileSharedState> {
  final _vibeFoldersReturn = Rxn<GetVibeFoldersByUserIdReturn>();
  final _memoryFoldersReturn = Rxn<GetMemoryFoldersByUserIdReturn>();

  ProfileContentController(AuthService authService, ProfileSharedState state) : super(authService, state);

  GetVibeFoldersByUserIdReturn? get vibeFoldersReturn => _vibeFoldersReturn.value;
  GetMemoryFoldersByUserIdReturn? get memoryFoldersReturn => _memoryFoldersReturn.value;

  @override
  Future<void> initController() async {
    super.initController();
    await loadFolders();
  }

  Future<void> loadFolders() async {
    await runSafe(tag: 'loadFolders', () async {
      final results = await Future.wait([
        authService.usersApi.getVibeFoldersByUserId(state.userId),
        authService.usersApi.getMemoryFoldersByUserId(state.userId),
      ]);

      _vibeFoldersReturn.value = results[0] as GetVibeFoldersByUserIdReturn?;
      _memoryFoldersReturn.value = results[1] as GetMemoryFoldersByUserIdReturn?;
    });
  }
}

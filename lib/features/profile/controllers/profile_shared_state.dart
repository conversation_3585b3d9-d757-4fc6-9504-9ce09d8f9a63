import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/shared_state.dart';

class ProfileSharedState extends SharedState {
  final String userId;

  ProfileSharedState(this.userId);

  final _userRole = Rx<UserRoleEnum?>(null);
  final _isFollowing = RxBool(false);
  final _relationshipStatus = Rx<UserRelationshipStatusEnum?>(null);
  final _profileViewCount = RxInt(0);
  final _isProfileLoading = RxBool(false);
  final _lastUpdated = Rx<DateTime?>(null);

  UserRoleEnum? get userRole => _userRole.value;
  set userRole(UserRoleEnum? value) => _userRole.value = value;

  bool get isFollowing => _isFollowing.value;
  set isFollowing(bool value) => _isFollowing.value = value;

  UserRelationshipStatusEnum? get relationshipStatus => _relationshipStatus.value;
  set relationshipStatus(UserRelationshipStatusEnum? value) => _relationshipStatus.value = value;

  int get profileViewCount => _profileViewCount.value;
  set profileViewCount(int value) => _profileViewCount.value = value;

  bool get isProfileLoading => _isProfileLoading.value;
  set isProfileLoading(bool value) => _isProfileLoading.value = value;

  DateTime? get lastUpdated => _lastUpdated.value;
  set lastUpdated(DateTime? value) => _lastUpdated.value = value;

  bool get isCreator => userRole == UserRoleEnum.creator;
  bool get isHighLevel => userRole == UserRoleEnum.level5 || userRole == UserRoleEnum.level6;
  bool get isFriendshipPending => relationshipStatus == UserRelationshipStatusEnum.pending;
  bool get areFriends => relationshipStatus == UserRelationshipStatusEnum.accepted;

  bool isOwnProfile(String currentUserId) => userId == currentUserId;

  void incrementViewCount() {
    profileViewCount = profileViewCount + 1;
  }

  void updateTimestamp() {
    lastUpdated = DateTime.now();
  }

  void resetProfileData() {
    userRole = null;
    isFollowing = false;
    relationshipStatus = null;
    profileViewCount = 0;
    isProfileLoading = false;
    lastUpdated = null;
  }

  void toggleFollowing() {
    isFollowing = !isFollowing;
    updateTimestamp();
  }

  void updateRelationshipStatus(UserRelationshipStatusEnum? status) {
    relationshipStatus = status;
    updateTimestamp();
  }

  bool get isProfileDataComplete {
    return userRole != null && lastUpdated != null;
  }

  Map<String, dynamic> get profileSummary {
    return {
      'userId': userId,
      'userRole': userRole?.value,
      'isFollowing': isFollowing,
      'relationshipStatus': relationshipStatus?.value,
      'profileViewCount': profileViewCount,
      'isProfileLoading': isProfileLoading,
      'lastUpdated': lastUpdated?.toIso8601String(),
      'isCreator': isCreator,
      'isHighLevel': isHighLevel,
      'isFriendshipPending': isFriendshipPending,
      'areFriends': areFriends,
    };
  }
}

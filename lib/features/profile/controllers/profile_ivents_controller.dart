import 'package:get/get.dart';
import 'package:ivent_app/core/constants/enums/user_type_enum.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_created_ivents_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_joined_ivents_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_shared_state.dart';
import 'package:ivent_app/features/profile/profile_pages.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';

class ProfileIventsController extends BaseController<ProfileSharedState> {
  late final ProfileCreatedIventsController createdIventsController;
  late final ProfileJoinedIventsController joinedIventsController;

  ProfileIventsController(AuthService authService, ProfileSharedState state) : super(authService, state);

  @override
  void initController() async {
    super.initController();
    final tag = state.userId;
    try {
      createdIventsController = Get.put(ProfileCreatedIventsController(authService, state), tag: tag);
      joinedIventsController = Get.put(ProfileJoinedIventsController(authService, state), tag: tag);
    } catch (e) {
      handleError(e);
    }
  }

  @override
  void closeController() {
    final tag = state.userId;
    try {
      Get.delete<ProfileCreatedIventsController>(tag: tag);
      Get.delete<ProfileJoinedIventsController>(tag: tag);
    } catch (e) {
      handleError(e);
    }
    super.closeController();
  }

  Future<void> goToIventsPage() async {
    if (state.userRole == UserTypeEnum.CREATOR) {
      Get.toNamed(ProfilePages.creatorIvents, arguments: state.userId);
    } else {
      Get.toNamed(ProfilePages.userIvents, arguments: state.userId);
    }
  }
}

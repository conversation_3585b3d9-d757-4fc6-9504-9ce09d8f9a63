import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_detail_state_manager.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';

class IventInfoController extends BaseController<IventDetailSharedState> {
  final _iventPage = Rxn<GetIventPageByIventIdReturn>();
  final _isFavorited = false.obs;

  IventInfoController(
    AuthService authService,
    IventDetailSharedState state,
  ) : super(authService, state);

  GetIventPageByIventIdReturn? get iventPage => _iventPage.value;
  bool get isFavorited => _isFavorited.value;

  set iventPage(GetIventPageByIventIdReturn? value) => _iventPage.value = value;
  set isFavorited(bool value) => _isFavorited.value = value;

  @override
  Future<void> initController() async {
    super.initController();
    await _loadIventData();
  }

  Future<void> toggleIventFavorite() async {
    await runSafe(
      () async {
        if (isFavorited) {
          await iventsApi.unfavoriteIventByIventId(state.iventId);
        } else {
          await iventsApi.favoriteIventByIventId(state.iventId);
        }

        isFavorited = !isFavorited;
      },
      tag: 'toggleIventFavorite',
    );
  }

  Future<void> refreshIventData() async {
    await _loadIventData();
  }

  Future<void> _loadIventData() async {
    await runSafe(
      () async {
        iventPage = await iventsApi.getIventPageByIventId(state.iventId);

        if (iventPage != null) {
          isFavorited = iventPage!.isFavorited ?? false;
        }
      },
      tag: 'loadIventData',
    );
  }
}

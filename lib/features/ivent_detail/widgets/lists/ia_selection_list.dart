import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/composite/buttons/shared_buttons.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_text_button.dart';
import 'package:ivent_app/features/ivent_detail/constants/strings.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_details_controller.dart';

/// A reusable widget for displaying expandable lists with selection functionality.
///
/// This widget provides a consistent interface for displaying lists that can be
/// expanded to show more items. It's commonly used for friend lists, group lists,
/// and other selectable collections in the ivent detail flow.
///
/// The widget shows a limited number of items initially and provides a "Show More"
/// button to expand the full list. It follows the project's design patterns for
/// list display and interaction. Follows the architecture guide's widget naming
/// conventions with "Ia" prefix.
///
/// Usage:
/// ```dart
/// IaSelectionList(
///   minShowLimit: 3,
///   itemCount: friends.length,
///   title: 'Friends',
///   listBuilder: (context, index) => FriendTile(friends[index]),
/// )
/// ```
class IaSelectionList extends StatefulWidget {
  /// Minimum number of items to show before requiring expansion
  final int minShowLimit;

  /// Total number of items in the list
  final int itemCount;

  /// Title to display above the list
  final String title;

  /// Builder function for creating list items
  final Widget Function(BuildContext, int) listBuilder;

  /// Creates a selection list widget.
  ///
  /// All parameters are required. The [listBuilder] function will be called
  /// for each visible item with the build context and item index.
  const IaSelectionList({
    super.key,
    required this.minShowLimit,
    required this.itemCount,
    required this.title,
    required this.listBuilder,
  });

  @override
  State<IaSelectionList> createState() => _IaSelectionListState();
}

class _IaSelectionListState extends State<IaSelectionList> {
  /// Whether the list is currently expanded to show all items
  bool _isListExtended = false;

  @override
  Widget build(BuildContext context) {
    final visibleItemCount = _isListExtended ? widget.itemCount : min(widget.minShowLimit, widget.itemCount);

    final hasMoreItems = widget.itemCount > widget.minShowLimit;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(widget.title, style: AppTextStyles.size14BoldPrimary),
        const SizedBox(height: AppDimensions.padding16),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: visibleItemCount,
          itemBuilder: widget.listBuilder,
          separatorBuilder: IaListTile.separatorBuilder20,
        ),
        if (hasMoreItems && !_isListExtended)
          Center(
            child: IaTextButton(
              margin: const EdgeInsets.only(top: AppDimensions.padding12),
              onPressed: () => setState(() => _isListExtended = true),
              text: IventDetailStrings.dahaFazlaGoster,
              textStyle: AppTextStyles.size12RegularTextSecondary,
            ),
          ),
      ],
    );
  }
}

// ============================================================================
// FACTORY FUNCTIONS FOR COMMON USE CASES
// ============================================================================

/// Creates a selection list specifically for friend groups.
///
/// This factory function creates a [SelectionList] configured for displaying
/// and selecting friend groups in the invitation flow. It handles the specific
/// data structure and interaction patterns for group selection.
///
/// Parameters:
/// - [invitableGroups]: The data containing available groups
/// - [controller]: The controller managing the selection state
Widget groupSelectionList(
  SearchInvitableUsersByIventIdReturn invitableGroups,
  IventDetailsController controller,
) {
  const int minGroupLimit = 2;
  return IaSelectionList(
    minShowLimit: minGroupLimit,
    itemCount: invitableGroups.groupCount,
    title: IventDetailStrings.arkadasGruplari,
    listBuilder: (context, index) {
      return Obx(() {
        final element = invitableGroups.groups[index];
        final isSelected = controller.invitationsController.selectedInvitableGroupIds.contains(element.groupId);

        return IaListTile.withImageUrl(
          avatarUrl: element.thumbnailUrl,
          title: element.groupName,
          subtitle: element.memberFirstnames.join(', '),
          // trailing: SharedButtons.checkBox(isSelected: isSelected),
          onTap: () => controller.invitationsController.toggleInvitableGroup(element),
        );
      });
    },
  );
}

/// Creates a selection list specifically for individual users/friends.
///
/// This factory function creates a [SelectionList] configured for displaying
/// and selecting individual friends in the invitation flow. It handles the
/// specific data structure and interaction patterns for user selection.
///
/// Parameters:
/// - [invitableUsers]: The data containing available users
/// - [controller]: The controller managing the selection state
Widget userSelectionList(
  SearchInvitableUsersByIventIdReturn invitableUsers,
  IventDetailsController controller,
) {
  const int minUserLimit = 5;
  return IaSelectionList(
    minShowLimit: minUserLimit,
    itemCount: invitableUsers.friendCount,
    title: IventDetailStrings.arkadaslar,
    listBuilder: (context, index) {
      return Obx(() {
        final element = invitableUsers.friends[index];
        final isSelected = controller.invitationsController.selectedInvitableUserIds.contains(element.userId);

        return IaListTile.withImageUrl(
          avatarUrl: element.avatarUrl,
          title: '@${element.username}',
          subtitle: element.university,
          // trailing: SharedButtons.checkBox(isSelected: isSelected),
          onTap: () => controller.invitationsController.toggleInvitableUser(element),
        );
      });
    },
  );
}

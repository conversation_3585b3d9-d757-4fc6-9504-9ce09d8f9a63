import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_collabs_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_detail_state_manager.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_details_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_info_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_invitations_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_participants_controller.dart';

class IventDetailBindings implements Bindings {
  @override
  void dependencies() {
    final service = Get.find<AuthService>();
    final iventId = Get.arguments as String;
    final sharedState = Get.put(IventDetailSharedState(iventId), tag: iventId, permanent: true);

    Get.lazyPut<IventDetailsController>(() => IventDetailsController(service, sharedState), tag: iventId, fenix: true);
    Get.lazyPut<IventInfoController>(() => IventInfoController(service, sharedState), tag: iventId, fenix: true);
    Get.lazyPut<IventParticipantsController>(() => IventParticipantsController(service, sharedState),
        tag: iventId, fenix: true);
    Get.lazyPut<IventInvitationsController>(() => IventInvitationsController(service, sharedState),
        tag: iventId, fenix: true);
    Get.lazyPut<IventCollabsController>(() => IventCollabsController(service, sharedState), tag: iventId, fenix: true);
  }
}

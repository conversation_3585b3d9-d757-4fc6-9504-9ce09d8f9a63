import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:flutter/widgets.dart';
import 'package:intl/intl.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';

class Calendar extends StatefulWidget {
  final Function(List<DateTime?> selectedDates) onDateRangeSelected;
  final VoidCallback onDateRangeAccepted;
  final List<DateTime?> selectedDates;

  const Calendar({
    super.key,
    required this.onDateRangeSelected,
    required this.onDateRangeAccepted,
    required List<DateTime?> this.selectedDates,
  });

  @override
  State<Calendar> createState() => _CalendarState();
}

class _CalendarState extends State<Calendar> {
  @override
  Widget build(BuildContext context) {
    return IaRoundedContainer(
      padding: const EdgeInsets.all(AppDimensions.padding12),
      width: MediaQuery.of(context).size.width - 40,
      roundness: AppDimensions.radiusXL,
      boxShadow: AppColors.usualShadow,
      color: AppColors.white,
      child: Column(
        children: [
          CalendarDatePicker2(
            value: widget.selectedDates,
            onValueChanged: widget.onDateRangeSelected,
            config: CalendarDatePicker2WithActionButtonsConfig(
              controlsHeight: 40,
              dayTextStyle: AppTextStyles.size14Bold,
              currentDate: DateTime.now(),
              firstDayOfWeek: 1,
              calendarType: CalendarDatePicker2Type.range,
              selectedRangeHighlightColor: AppColors.primaryLight,
              selectedDayTextStyle: AppTextStyles.size14Bold,
              selectedDayHighlightColor: AppColors.primary,
              centerAlignModePicker: true,
            ),
          ),
          _buildButtonRow(),
        ],
      ),
    );
  }

  Row _buildButtonRow() {
    return Row(
      children: [
        Expanded(child: _buildFirstButton()),
        const SizedBox(width: 20),
        Expanded(child: _buildSecondButton()),
      ],
    );
  }

  Widget _buildFirstButton() {
    var text;
    if (widget.selectedDates.isEmpty) {
      text = 'Başlangıç Tarihi Seçiniz';
    } else if (widget.selectedDates.length == 1) {
      text = 'Bitiş Tarihi Seçiniz';
    } else if (widget.selectedDates.length == 2) {
      final startDate = DateFormat('d MMM').format(widget.selectedDates[0]!);
      final endDate = DateFormat('d MMM').format(widget.selectedDates[1]!);
      text = '$startDate - $endDate';
    }
    return _buildButton(
      child: Text(
        text,
        style: AppTextStyles.size14Medium,
        overflow: TextOverflow.fade,
        maxLines: 1,
        softWrap: false,
      ),
    );
  }

  Widget _buildSecondButton() {
    return _buildButton(
      onTap: () {
        if (widget.selectedDates.length == 2) {
          widget.onDateRangeAccepted();
        }
      },
      color: widget.selectedDates.length == 2 ? AppColors.primary : AppColors.lightGrey,
      child: Text(
        'Bitti',
        style: widget.selectedDates.length == 2 ? AppTextStyles.size14Medium : AppTextStyles.size14Medium,
        overflow: TextOverflow.fade,
        maxLines: 1,
        softWrap: false,
      ),
    );
  }

  GestureDetector _buildButton({
    required Widget child,
    Color color = AppColors.lightGrey,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: color,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 5),
        height: 33,
        child: Center(child: child),
      ),
    );
  }
}

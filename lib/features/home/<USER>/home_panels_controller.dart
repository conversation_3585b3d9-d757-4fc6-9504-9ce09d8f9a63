import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/features/home/<USER>';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

class HomePanelsController extends BaseController<HomeSharedState> {
  final PanelController panelController = PanelController();

  final isPanelDraggable = true.obs;
  final isPanelVisible = true.obs;
  final screenIndex = 0.obs;

  HomePanelsController(AuthService authService, HomeSharedState state) : super(authService, state);

  void goToFeedPage() {
    screenIndex.value = 0;
    isPanelDraggable.value = true;
    panelController.open();
  }

  void goToSearchPage() {
    screenIndex.value = 1;
    isPanelDraggable.value = false;
    panelController.open();
  }

  void goToFilterPage() {
    screenIndex.value = 2;
    isPanelDraggable.value = false;
    panelController.open();
  }

  void goToLocationPage() => Get.toNamed(HomePages.locationPage);

  void setPanelOpen() => isPanelVisible.value = true;

  void setPanelClosed() => isPanelVisible.value = false;
}

import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/features/home/<USER>/feed_filters.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';

class HomeFeedController extends BaseController<HomeSharedState> {
  final iventItems = <IventCardItem>[].obs;
  final existingFeedFilters = Rxn<FeedFilters>();
  final feedPage = 1.obs;
  final feedContinuable = true.obs;

  HomeFeedController(AuthService authService, HomeSharedState state) : super(authService, state);

  @override
  Future<void> initController() async {
    super.initController();
    await retrieveIventList();
  }

  Future<void> retrieveIventList() async {
    await runSafe(
      () async {
        final params = _buildFeedParams();
        existingFeedFilters.value = params;

        final response = await homeApi.feed(
          params.dateType,
          params.categories,
          locationCoeff: params.locationCoeff,
          q: params.q,
          page: 1,
          latitude: params.latitude,
          longitude: params.longitude,
          endDate: params.endDate,
          startDate: params.startDate,
        );

        if (response != null && response.ivents.isNotEmpty) {
          iventItems.value = response.ivents;
        }
      },
      tag: 'feed',
    );
  }

  Future<void> loadMoreIventItems() async {
    if (!feedContinuable.value) return;

    await runSafe(
      () async {
        feedPage.value++;

        final response = await homeApi.feed(
          existingFeedFilters.value!.dateType,
          existingFeedFilters.value!.categories,
          locationCoeff: existingFeedFilters.value!.locationCoeff,
          q: existingFeedFilters.value!.q,
          page: feedPage.value,
          latitude: existingFeedFilters.value!.latitude,
          longitude: existingFeedFilters.value!.longitude,
          endDate: existingFeedFilters.value!.endDate,
          startDate: existingFeedFilters.value!.startDate,
        );

        if (response != null && response.ivents.isNotEmpty) {
          iventItems.value = [...iventItems, ...response.ivents];
        } else {
          feedContinuable.value = false;
        }
      },
      tag: 'feed',
      onError: () => feedPage.value--,
    );
  }

  void toggleTimeFilter(int index) {
    state.dateFilterIndex.value = index;

    if (index != 0) state.selectedDates.value = [];

    feedPage.value = 1;
    retrieveIventList();
  }

  void setSelectedDates(DateTime startDate, DateTime endDate) {
    state.selectedDates.value = [startDate, endDate];

    state.dateFilterIndex.value = FeedDateEnum.values.indexOf(FeedDateEnum.range);

    feedPage.value = 1;
    retrieveIventList();
  }

  String selectedDatesAsString() {
    if (state.selectedDates.isEmpty) return 'Başlangıç Tarihi Seçiniz';
    if (state.selectedDates.length == 1) return 'Bitiş Tarihi Seçiniz';
    final format = DateFormat('d MMM');
    final startDate = format.format(state.selectedDates[0]);
    final endDate = format.format(state.selectedDates[1]);
    return '$startDate - $endDate';
  }

  void applyFilters() {
    feedPage.value = 1;
    retrieveIventList();
  }

  FeedFilters _buildFeedParams() {
    final params = FeedFilters(
      dateType: FeedDateEnum.values[state.dateFilterIndex.value],
      categories: '',
      locationCoeff: state.locationCoeff.value,
      q: '',
    );

    if (state.selectedHobbyIds.isNotEmpty) {
      params.categories = state.selectedHobbyIds.join(',');
    }

    if (state.selectedPlace.value != null) {
      params.latitude = state.selectedPlace.value!.latitude;
      params.longitude = state.selectedPlace.value!.longitude;
    }

    if (state.selectedDates.length == 2) {
      params.startDate = _formatDateForApi(state.selectedDates[0]);
      params.endDate = _formatDateForApi(state.selectedDates[1]);
    }

    return params;
  }

  String _formatDateForApi(DateTime date) => DateFormat('yyyy-MM-dd').format(date);
}

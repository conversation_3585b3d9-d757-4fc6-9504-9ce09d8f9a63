import 'package:flutter/material.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/shared/widgets/ivent_box_list_view.dart';

class MapIventBox extends StatelessWidget {
  final IventCardItem iventBanner;

  const MapIventBox({
    super.key,
    required this.iventBanner,
  });

  @override
  Widget build(BuildContext context) {
    return _buildIventBox(context);
  }

  Widget _buildIventBox(BuildContext context) {
    return IventBoxListView(
      width: 190 * 0.87, // TODO: Move to constants
      height: 190,
      color: AppColors.white,
      iventId: iventBanner.iventId,
      iventName: iventBanner.iventName,
      thumbnailUrl: iventBanner.thumbnailUrl,
      locationName: iventBanner.locationName,
      creatorId: iventBanner.creatorId,
      creatorName: iventBanner.creatorUsername,
      creatorImageUrl: iventBanner.creatorImageUrl,
      isFavorited: iventBanner.isFavorited,
      onFavorite: () {},
    );
  }
}

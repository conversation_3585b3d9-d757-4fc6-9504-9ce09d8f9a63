import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/features/home/<USER>/common/calendar.dart';
import 'package:ivent_app/features/home/<USER>/map_page/buttons/map_date_button.dart';

class MapScreenDatePicker extends StatefulWidget {
  final String todayAsString;

  const MapScreenDatePicker({
    super.key,
    required this.todayAsString,
  });

  @override
  State<MapScreenDatePicker> createState() => _MapScreenDatePickerState();
}

class _MapScreenDatePickerState extends State<MapScreenDatePicker> {
  bool _isCalendarVisible = false;
  List<DateTime?> _selectedDates = [];

  void _toggleCalendarVisibility() => setState(() => _isCalendarVisible = !_isCalendarVisible);

  void _applySelectedDates() => _toggleCalendarVisibility();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        MapDateButton(
          onTap: _toggleCalendarVisibility,
          text: widget.todayAsString,
        ),
        const SizedBox(height: AppDimensions.padding20),
        if (_isCalendarVisible)
          Calendar(
            selectedDates: _selectedDates,
            onDateRangeSelected: (dates) => setState(() => _selectedDates = dates),
            onDateRangeAccepted: _applySelectedDates,
          ),
      ],
    );
  }
}

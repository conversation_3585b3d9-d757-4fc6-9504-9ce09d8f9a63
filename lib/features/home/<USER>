import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/home_accounts_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_feed_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_filter_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_ivents_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_location_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_map_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_panels_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_search_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/features/notifications/controllers/notification_controller.dart';
import 'package:ivent_app/features/profile/profile_bindings.dart';
import 'package:ivent_app/features/side_menu/controllers/profile_side_menu_controller.dart';
import 'package:ivent_app/features/vibes/controllers/vibe_upload_controller.dart';
import 'package:ivent_app/features/vibes/controllers/vibes_page_controller.dart';
import 'package:ivent_app/features/vibes/controllers/vibes_state_manager.dart';
import 'package:ivent_app/core/controllers/shared_state.dart';

class HomeBindings implements Bindings {
  @override
  void dependencies() {
    final service = Get.find<AuthService>();
    final sessionId = service.sessionUser!.sessionId;

    final defaultSharedState = Get.put(SharedState(), permanent: true);
    final homeSharedState = Get.put(HomeSharedState(), permanent: true);
    final vibesSharedState = Get.put(VibesSharedState(), permanent: true);

    ProfileBindings(userId: sessionId, permanent: true).dependencies();

    Get.lazyPut(() => HomeAccountsController(service, homeSharedState), fenix: true);
    Get.lazyPut(() => HomeFeedController(service, homeSharedState), fenix: true);
    Get.lazyPut(() => HomeFilterController(service, homeSharedState), fenix: true);
    Get.lazyPut(() => HomeIventsController(service, homeSharedState), fenix: true);
    Get.lazyPut(() => HomeSearchController(service, homeSharedState), fenix: true);
    Get.lazyPut(() => HomePanelsController(service, homeSharedState), fenix: true);
    Get.lazyPut(() => HomeMapController(service, homeSharedState), fenix: true);
    Get.lazyPut(() => HomeLocationController(service, homeSharedState), fenix: true);

    Get.lazyPut(() => VibesPageController(service, vibesSharedState), fenix: true);
    Get.lazyPut(() => VibeUploadController(service, vibesSharedState), fenix: true);
    Get.lazyPut(() => NotificationController(service, defaultSharedState), fenix: true);
    Get.lazyPut(() => ProfileSideMenuController(service, defaultSharedState), fenix: true);
  }
}

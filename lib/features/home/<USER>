import 'package:get/get.dart';
import 'package:ivent_app/core/middlewares/auth_middleware.dart';
import 'package:ivent_app/features/app_navigation/pages/app_navigation_screen.dart';
import 'package:ivent_app/features/home/<USER>';
import 'package:ivent_app/features/home/<USER>/home_page.dart';
import 'package:ivent_app/features/home/<USER>/location_page.dart';

class HomePages {
  HomePages._();

  static const _prefix = '/home';

  static const appNavigation = '$_prefix/appNavigation';
  static const homePage = '$_prefix/homePage';
  static const locationPage = '$_prefix/locationPage';

  static final routes = [
    GetPage(
      name: appNavigation,
      page: () => const AppNavigationScreen(),
      binding: HomeBindings(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: homePage,
      page: () => const HomePage(),
    ),
    GetPage(
      name: locationPage,
      page: () => const LocationPage(),
    ),
  ];
}

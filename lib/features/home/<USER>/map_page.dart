import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/features/home/<USER>/home_map_controller.dart';
import 'package:ivent_app/features/home/<USER>/map_page/map_action_buttons.dart';
import 'package:ivent_app/features/home/<USER>/map_page/map_date_picker.dart';
import 'package:ivent_app/features/home/<USER>/map_page/map_ivents.dart';
import 'package:ivent_app/features/mapbox/widgets/ia_map_widget.dart';

class MapPage extends GetView<HomeMapController> {
  const MapPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // TODO: Handle this map controller mess
        IaMapWidget(
          onMapCreated: controller.mapboxController.setMapboxMap,
          onBoundsChanged: controller.updateVisibleMapBounds,
          onFeatureSelected: controller.markerController.handleFeatureClick,
        ),
        Positioned(
          left: AppDimensions.padding20,
          top: AppDimensions.padding20,
          child: MapScreenDatePicker(todayAsString: controller.todayAsString),
        ),
        const MapActionButtons(),
        Obx(() => MapIvents(iventBanners: controller.iventBanners)),
      ],
    );
  }
}

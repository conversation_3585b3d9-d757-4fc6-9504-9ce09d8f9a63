import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';

class HomeIventsController extends BaseControllerWithSearch<HomeSharedState> {
  HomeIventsController(AuthService authService, HomeSharedState state) : super(authService, state);

  final searchedIventsResults = <IventCardItem>[].obs;

  @override
  bool get isResultsEmpty => searchedIventsResults.isEmpty;

  @override
  InitialSearchBehavior get initialSearchBehavior => InitialSearchBehavior.mustSearch;

  @override
  Future<void> onSearch([String? query]) async {
    if (query == null || query.isEmpty) {
      searchedIventsResults.value = [];
      return;
    }

    final response = await homeApi.searchIvent(query);
    if (response != null) {
      searchedIventsResults.value = response.ivents;
    } else {
      searchedIventsResults.value = [];
    }
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/home/<USER>/home_location_controller.dart';
import 'package:ivent_app/features/home/<USER>/location_page/feed_location_use_my_location_tile.dart';

class LocationPage extends GetView<HomeLocationController> {
  const LocationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return IaScaffold.search(
      title: 'Lokasyon Seç',
      textEditingController: controller.textController,
      body: Column(
        children: [
          const FeedLocationUseMyLocationTile(),
          const SizedBox(height: AppDimensions.padding20),
          Expanded(child: _buildSearchResults()),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    return Obx(() {
      final suggestions = controller.placeSuggestionResults;
      return IaSearchPlaceholder(
        entityName: 'Konum',
        iconPath: AppAssets.mapPin,
        isSearching: controller.isSearching,
        isResultsEmpty: controller.isResultsEmpty,
        isQueryEmpty: controller.isQueryEmpty,
        initialSearchBehavior: InitialSearchBehavior.mustSearch,
        builder: (context) {
          return ListView.separated(
            padding: const EdgeInsets.only(bottom: 100),
            itemCount: suggestions.length,
            itemBuilder: (context, index) {
              final suggestion = suggestions[index];
              return IaListTile(
                title: suggestion.name,
                subtitle: suggestion.fullAddress ?? suggestion.placeFormatted,
                onTap: () {
                  controller.selectPlace(suggestion);
                  Get.back();
                },
              );
            },
            separatorBuilder: IaListTile.separatorBuilder20,
          );
        },
      );
    });
  }
}

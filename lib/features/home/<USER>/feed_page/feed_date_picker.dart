import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/features/home/<USER>/common/calendar.dart';
import 'package:ivent_app/features/home/<USER>/feed_page/feed_date_scroll.dart';

class FeedDatePicker extends StatefulWidget {
  final Function(DateTime startDate, DateTime endDate) onDateRangeSelected;
  final Function(int index) onDateButtonPressed;

  const FeedDatePicker({
    super.key,
    required this.onDateRangeSelected,
    required this.onDateButtonPressed,
  });

  @override
  State<FeedDatePicker> createState() => _FeedDatePickerState();
}

class _FeedDatePickerState extends State<FeedDatePicker> {
  bool _isCalendarVisible = false;
  int? _selectedIndex = null;
  List<DateTime?> _selectedDates = [];

  void _toggleCalendarVisibility() => setState(() => _isCalendarVisible = !_isCalendarVisible);

  void _onDateButtonPressed(int index) => setState(() {
        _selectedIndex = index;

        if (index == 0) {
          _onCalenderButtonPressed();
        } else {
          if (_isCalendarVisible) {
            _toggleCalendarVisibility();
          }
          if (index != 1) {
            _selectedDates = [];
          }
        }
        widget.onDateButtonPressed(index);
      });

  void _onCalenderButtonPressed() {
    if (_isCalendarVisible && _selectedDates.length == 2) {
      widget.onDateRangeSelected(_selectedDates[0]!, _selectedDates[1]!);
    } else if (_isCalendarVisible) {
      _selectedIndex = null;
    }
    _toggleCalendarVisibility();
  }

  void _applySelectedDates() => _toggleCalendarVisibility();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding20),
      child: Column(
        children: [
          FeedDateScroll(
            selectedIndex: _selectedIndex,
            onDateButtonPressed: _onDateButtonPressed,
          ),
          if (_isCalendarVisible) const SizedBox(height: AppDimensions.padding20),
          if (_isCalendarVisible)
            Calendar(
              selectedDates: _selectedDates,
              onDateRangeSelected: (dates) => setState(() => _selectedDates = dates),
              onDateRangeAccepted: _applySelectedDates,
            ),
        ],
      ),
    );
  }
}

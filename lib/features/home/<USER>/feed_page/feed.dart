import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/features/home/<USER>/home_feed_controller.dart';
import 'package:ivent_app/shared/widgets/ivent_grid.dart';

class Feed extends GetView<HomeFeedController> {
  const Feed({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      child: Obx(() {
        final ivents = controller.iventItems;
        return IaSearchPlaceholder(
          entityName: 'iVent',
          isSearching: controller.isLoading(),
          isResultsEmpty: ivents.isEmpty,
          isQueryEmpty: true,
          initialSearchBehavior: InitialSearchBehavior.loaded,
          builder: (context) {
            return Align(
              alignment: Alignment.topCenter,
              child: IventGrid(
                iventItems: ivents,
                onLoadMore: controller.loadMoreIventItems,
                onRefresh: controller.retrieveIventList,
              ),
            );
          },
        );
      }),
    );
  }
}

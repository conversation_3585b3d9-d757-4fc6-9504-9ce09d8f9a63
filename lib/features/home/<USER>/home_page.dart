import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/widgets/layout/panels/ia_bottom_panel.dart';
import 'package:ivent_app/core/widgets/layout/panels/ia_sliding_panel.dart';
import 'package:ivent_app/features/home/<USER>/home_panels_controller.dart';
import 'package:ivent_app/features/home/<USER>/feed_page.dart';
import 'package:ivent_app/features/home/<USER>/filter_page.dart';
import 'package:ivent_app/features/home/<USER>/map_page.dart';
import 'package:ivent_app/features/home/<USER>/search_page.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

class HomePage extends GetView<HomePanelsController> {
  const HomePage({super.key});

  static const List<Widget> _screens = [
    FeedPage(),
    SearchScreen(),
    FilterPage(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        return IaSlidingPanel(
          defaultPanelState: PanelState.CLOSED,
          panelController: controller.panelController,
          isDraggable: controller.isPanelDraggable.value,
          maxHeight: Get.height,
          minHeight: 70, // TODO: Adjust this and move to constants
          onPanelOpened: controller.setPanelOpen,
          onPanelClosed: controller.setPanelClosed,
          panel: IaBottomPanel(
            showSlideIndicator: controller.isPanelDraggable.value,
            body: _screens[controller.screenIndex.value],
          ),
          body: const SafeArea(child: MapPage()), // TODO: Safe area will be removed
        );
      }),
    );
  }
}

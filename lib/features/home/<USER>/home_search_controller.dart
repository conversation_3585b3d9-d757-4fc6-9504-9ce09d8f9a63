import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/home_accounts_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_ivents_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';

class HomeSearchController extends BaseController<HomeSharedState> {
  late final HomeAccountsController searchAccountsController;
  late final HomeIventsController searchIventsController;

  HomeSearchController(AuthService authService, HomeSharedState state) : super(authService, state);

  final searchTabIndex = 0.obs;

  @override
  void initController() async {
    super.initController();
    searchAccountsController = Get.put(HomeAccountsController(authService, state));
    searchIventsController = Get.put(HomeIventsController(authService, state));
  }

  @override
  void closeController() {
    Get.delete<HomeAccountsController>();
    Get.delete<HomeIventsController>();
    super.closeController();
  }

  void changeSearchTab(int index) => searchTabIndex.value = index;
}

import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/features/settings/constants/settings_constants.dart';
import 'package:ivent_app/features/settings/controllers/settings_controller.dart';
import 'package:ivent_app/features/settings/widgets/common/settings_section_header.dart';
import 'package:ivent_app/features/settings/widgets/ia_settings_list_tile.dart';

class SettingsMenuSection extends StatelessWidget {
  final SettingsController controller;

  const SettingsMenuSection({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SettingsSectionHeader(title: SettingsConstants.privacySectionTitle),
        IaSettingsListTile(
          icon: AppAssets.shield,
          title: SettingsConstants.privacySettingsItem,
          subtitle: SettingsConstants.privacySettingsSubtitle,
          onTap: controller.goToPrivacySettings,
        ),
        const SizedBox(height: AppDimensions.padding12),
        const SettingsSectionHeader(title: SettingsConstants.securitySectionTitle),
        IaSettingsListTile(
          icon: AppAssets.lock,
          title: SettingsConstants.securitySettingsItem,
          subtitle: SettingsConstants.securitySettingsSubtitle,
          onTap: controller.goToSecuritySettings,
        ),
        const SizedBox(height: AppDimensions.padding12),
        const SettingsSectionHeader(title: SettingsConstants.supportSectionTitle),
        IaSettingsListTile(
          icon: AppAssets.help,
          title: SettingsConstants.supportItem,
          subtitle: SettingsConstants.supportSubtitle,
          onTap: controller.goToSupportPage,
        ),
        const SizedBox(height: AppDimensions.padding12),
        const SettingsSectionHeader(title: SettingsConstants.aboutSectionTitle),
        IaSettingsListTile(
          icon: AppAssets.info,
          title: SettingsConstants.aboutItem,
          subtitle: SettingsConstants.aboutSubtitle,
          onTap: controller.goToAboutPage,
        ),
        const SizedBox(height: AppDimensions.padding32),
        const SettingsSectionHeader(title: SettingsConstants.accountSectionTitle),
        IaSettingsListTile(
          icon: AppAssets.logOut,
          title: SettingsConstants.logoutItem,
          subtitle: SettingsConstants.logoutSubtitle,
          onTap: controller.logout,
          titleColor: AppColors.warning,
        ),
        const SizedBox(height: AppDimensions.padding8),
        IaSettingsListTile(
          icon: AppAssets.deleteColumn,
          title: SettingsConstants.deleteAccountItem,
          subtitle: SettingsConstants.deleteAccountSubtitle,
          onTap: () => controller.deleteAccount(),
          titleColor: AppColors.error,
        ),
      ],
    );
  }
}

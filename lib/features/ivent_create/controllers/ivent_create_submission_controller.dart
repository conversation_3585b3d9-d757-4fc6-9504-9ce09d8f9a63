import 'package:get/get.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_state_manager.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

class IventCreateSubmissionController extends BaseController<IventCreateSharedState> {
  IventCreateSubmissionController(AuthService authService, IventCreateSharedState state) : super(authService, state);

  final PanelController panelController = PanelController();
  final selectedPanelIndex = 0.obs;
  final isPanelVisible = false.obs;

  void openPanel(int panelIndex) {
    isPanelVisible.value = true;
    selectedPanelIndex.value = panelIndex;
    panelController.open();
  }

  void closePanel() {
    isPanelVisible.value = false;
    panelController.close();
  }
}

import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_state_manager.dart';
import 'package:ivent_app/features/ivent_create/ivent_create_pages.dart';

class IventCreateImageController extends BaseController<IventCreateSharedState> {
  IventCreateImageController(AuthService authService, IventCreateSharedState state) : super(authService, state);

  final textController = TextEditingController();
  final suggestedImagesReturn = Rxn<GetSuggestedImagesReturn>();

  List<String> get imageUrls => suggestedImagesReturn.value?.imageUrls ?? [];

  String get iventName => state.iventName.value;
  String? get selectedImageUrl => state.selectedImageUrl.value;
  Uint8List? get selectedImageFile => state.selectedImageFile.value;

  bool get isNameValid => iventName.length > 3;
  bool get isUrlSelected => selectedImageUrl != null;
  bool get isFileSelected => selectedImageFile != null;
  bool get isImageSelected => isUrlSelected || isFileSelected;
  bool get canContinue => isImageSelected && isNameValid;

  void handleIventNameChanged(String value) => state.iventName.value = value;
  void handleSelectedImageUrlChanged(String value) => state.selectedImageUrl.value = value;
  void handleSelectedImageFileChanged(Uint8List value) => state.selectedImageFile.value = value;
  void goToDateSelectionPage() => Get.toNamed(IventCreatePages.dateSelection);

  void goToImageGalleryPage() => !isUrlSelected ? Get.toNamed(IventCreatePages.imageGallery) : null;

  @override
  Future<void> initController() async {
    super.initController();
    await runSafe(tag: 'getSuggestedImages', () async {
      suggestedImagesReturn.value = await iventsApi.getSuggestedImages('');
    });
  }
}

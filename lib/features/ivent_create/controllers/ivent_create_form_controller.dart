import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/foundation/dialogs/ia_alert_dialog.dart';
import 'package:ivent_app/features/home/<USER>';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_state_manager.dart';
import 'package:ivent_app/features/ivent_create/ivent_create_pages.dart';
import 'package:ivent_app/shared/domain/entities/hobby.dart';

class IventCreateFormController extends BaseController<IventCreateSharedState> {
  IventCreateFormController(AuthService authService, IventCreateSharedState state) : super(authService, state);

  final selectedCategory = Rxn<Hobby>();
  final selectedSubCategory = Rxn<Hobby>();
  final description = Rxn<String>();
  final selectedTags = <Hobby>[].obs;
  final selectedIventPrivacy = IventPrivacyEnum.public.obs;
  final googleFormsUrl = Rxn<String>();
  final instagramUsername = Rxn<String>();
  final whatsappUrl = Rxn<String>();
  final isWhatsappUrlPrivate = false.obs;
  final whatsappNumber = Rxn<String>();
  final callNumber = Rxn<String>();
  final websiteUrl = Rxn<String>();

  bool get isThereAnyRegisterType =>
      (googleFormsUrl.value != null && googleFormsUrl.value!.isNotEmpty) ||
      (instagramUsername.value != null && instagramUsername.value!.isNotEmpty) ||
      (whatsappUrl.value != null && whatsappUrl.value!.isNotEmpty) ||
      (whatsappNumber.value != null && whatsappNumber.value!.isNotEmpty) ||
      (callNumber.value != null && callNumber.value!.isNotEmpty) ||
      (websiteUrl.value != null && websiteUrl.value!.isNotEmpty);

  List<String> get selectedTagIds => selectedTags.map((var element) => element.hobbyId).toList();
  List<String> get selectedTagNames => selectedTags.map((var element) => element.hobbyName).toList();

  void handleMainCategorySelected(Hobby category) {
    selectedCategory.value = category;
    Get.toNamed(IventCreatePages.subcategorySelection);
  }

  void handleSubCategorySelected(Hobby category) {
    selectedSubCategory.value = category;
    Get.toNamed(IventCreatePages.imageSelection);
  }

  void toggleTag(Hobby tag) {
    if (selectedTagIds.contains(tag.hobbyId)) {
      selectedTags.removeWhere((element) => element.hobbyId == tag.hobbyId);
    } else {
      selectedTags.add(tag);
    }
  }

  Future<void> submitIvent(BuildContext context) async {
    await runSafe(tag: 'submitIvent', () async {
      await iventsApi.createIvent(
        CreateIventDto(
          creatorType: AccountTypeEnum.user,
          iventName: state.iventName.value,
          thumbnailBuffer: state.selectedImageFile.value != null ? base64Encode(state.selectedImageFile.value!) : null,
          dates: state.dates.map((var date) => date.toIso8601String()).toList(),
          latitude: state.selectedPlace.value!.latitude,
          longitude: state.selectedPlace.value!.longitude,
          categoryTagId: selectedSubCategory.value!.hobbyId,
          privacy: selectedIventPrivacy.value,
          tagIds: selectedTagIds,
          allowedUniversityCodes: [],
          collabs: [],
          description: description.value,
          googleFormsUrl: googleFormsUrl.value,
          instagramUsername: instagramUsername.value,
          whatsappUrl: whatsappUrl.value,
          whatsappNumber: whatsappNumber.value,
          callNumber: callNumber.value,
          websiteUrl: websiteUrl.value,
          mapboxId: state.selectedPlace.value!.mapboxId!,
        ),
      );

      Get.toNamed(HomePages.appNavigation);
      Get.dialog(
        IaAlertDialog.iventCreate(
          iventName: state.iventName.value,
          onOk: () => Get.back(),
          onCancel: () => Get.back(),
        ),
      );
    });
  }
}

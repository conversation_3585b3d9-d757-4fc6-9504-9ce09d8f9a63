import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/layout/panels/ia_bottom_panel.dart';
import 'package:ivent_app/core/widgets/layout/panels/ia_sliding_panel.dart';
import 'package:ivent_app/core/widgets/specialized/ivent/ivent_create_buttons.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_map_controller.dart';
import 'package:ivent_app/features/ivent_create/widgets/panels/ia_ivent_create_place_search_panel.dart';
import 'package:ivent_app/features/ivent_create/widgets/panels/ia_ivent_create_selected_place_panel.dart';
import 'package:ivent_app/features/mapbox/widgets/ia_map_widget.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

class MapSelection extends GetView<IventCreateMapController> {
  const MapSelection({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        return IaSlidingPanel(
          panelController: controller.panelController,
          defaultPanelState: PanelState.CLOSED,
          isDraggable: !_isPlaceSelected,
          maxHeight: Get.height * 0.8,
          minHeight: _isPlaceSelected ? 115 + AppDimensions.buttonHeightLongBar : 90,
          panel: IaBottomPanel(
            showSlideIndicator: true,
            // body: _isPlaceSelected ? const _SelectedPlacePanel() : const _PlaceSearchPanel(),
            body: Container(),
          ),
          body: const SafeArea(
            child: _MapBody(),
          ),
        );
      }),
    );
  }

  bool get _isPlaceSelected => controller.state.selectedPlace.value != null;
}

// class _SelectedPlacePanel extends GetView<IventCreateMapController> {
//   const _SelectedPlacePanel();

//   @override
//   Widget build(BuildContext context) {
//     return IaIventCreateSelectedPlacePanel(controller: Get.find());
//   }
// }

// class _PlaceSearchPanel extends GetView<IventCreateMapController> {
//   const _PlaceSearchPanel();

//   @override
//   Widget build(BuildContext context) {
//     return IaIventCreatePlaceSearchPanel(controller: Get.find());
//   }
// }

class _MapBody extends GetView<IventCreateMapController> {
  const _MapBody();

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: IaMapWidget(
            onMapCreated: controller.mapboxController.setMapboxMap,
          ),
        ),
        const _ActionButtons(),
      ],
    );
  }
}

class _ActionButtons extends GetView<IventCreateMapController> {
  const _ActionButtons();

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 20,
      left: 20,
      right: 20,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IventCreateButtons.mapBackButton(),
          IventCreateButtons.findUserLocation(
            onTap: controller.mapboxController.moveCameraToUserLocation,
          ),
        ],
      ),
    );
  }
}

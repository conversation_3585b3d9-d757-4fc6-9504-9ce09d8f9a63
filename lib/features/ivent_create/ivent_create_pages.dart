import 'package:get/get.dart';
import 'package:ivent_app/features/ivent_create/ivent_create_bindings.dart';
import 'package:ivent_app/features/ivent_create/views/category_selection.dart';
import 'package:ivent_app/features/ivent_create/views/date_selection.dart';
import 'package:ivent_app/features/ivent_create/views/description.dart';
import 'package:ivent_app/features/ivent_create/views/image_gallery.dart';
import 'package:ivent_app/features/ivent_create/views/image_selection.dart';
import 'package:ivent_app/features/ivent_create/views/map_selection.dart';
import 'package:ivent_app/features/ivent_create/views/preview.dart';
import 'package:ivent_app/features/ivent_create/views/subcategory_selection.dart';
import 'package:ivent_app/features/ivent_create/views/tag_catalogue.dart';
import 'package:ivent_app/features/ivent_create/widgets/panels/ia_ivent_create_privacy_panel.dart';
import 'package:ivent_app/features/ivent_create/widgets/panels/ia_ivent_create_register_type_panel.dart';

class IventCreatePages {
  IventCreatePages._();

  static const _prefix = '/iventCreate';

  static const categorySelection = '$_prefix/categorySelection';
  static const subcategorySelection = '$_prefix/subcategorySelection';
  static const imageSelection = '$_prefix/imageSelection';
  static const imageGallery = '$_prefix/imageGallery';
  static const dateSelection = '$_prefix/dateSelection';
  static const mapSelection = '$_prefix/mapSelection';
  static const preview = '$_prefix/preview';
  static const description = '$_prefix/description';
  static const tagCatalogue = '$_prefix/tagCatalogue';
  static const registerType = '$_prefix/registerType';
  static const privacy = '$_prefix/privacy';

  static final routes = [
    GetPage(
      name: categorySelection,
      page: () => const CategorySelection(),
      binding: IventCreateBindings(),
    ),
    GetPage(
      name: subcategorySelection,
      page: () => const SubcategorySelection(),
    ),
    GetPage(
      name: imageSelection,
      page: () => const ImageSelection(),
    ),
    GetPage(
      name: imageGallery,
      page: () => const ImageGallery(),
    ),
    GetPage(
      name: dateSelection,
      page: () => DateSelection(),
    ),
    GetPage(
      name: mapSelection,
      page: () => const MapSelection(),
    ),
    GetPage(
      name: preview,
      page: () => const Preview(),
    ),
    GetPage(
      name: description,
      page: () => const Description(),
    ),
    GetPage(
      name: tagCatalogue,
      page: () => const TagCatalogue(),
    ),
    // GetPage(
    //   name: registerType,
    //   page: () => const IaIventCreateRegisterTypePanel(),
    // ),
    // GetPage(
    //   name: privacy,
    //   page: () => const IaIventCreatePrivacyPanel(),
    // ),
  ];
}

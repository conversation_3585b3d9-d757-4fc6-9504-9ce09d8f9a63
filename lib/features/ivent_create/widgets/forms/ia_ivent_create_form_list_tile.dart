import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/composite/buttons/shared_buttons.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';
import 'package:ivent_app/core/widgets/foundation/inputs/ia_checkbox.dart';

/// A specialized form input tile for ivent creation forms.
///
/// This widget provides a consistent interface for form inputs throughout the
/// ivent creation flow. It displays an icon, input field, subtitle, and a
/// checkbox indicator showing whether the field has been filled.
///
/// The widget supports various predefined configurations for common form types
/// like Google Forms, Instagram, WhatsApp, etc., while also allowing custom
/// configurations.
///
/// Follows the architecture guide's widget naming conventions with "Ia" prefix
/// and proper widget structure patterns.
class IaIventCreateFormListTile extends StatefulWidget {
  /// Optional margin around the tile
  final EdgeInsetsGeometry? margin;

  /// Optional padding inside the tile
  final EdgeInsetsGeometry? padding;

  /// Callback function called when the input text changes
  final Function(String) onChanged;

  /// Placeholder text for the input field
  final String hintText;

  /// Subtitle text displayed below the input field
  final String subtitle;

  /// Background color for the icon circle
  final Color color;

  /// Path to the SVG icon asset
  final String iconPath;

  /// Optional color for the icon (if null, uses default)
  final Color? iconColor;

  const IaIventCreateFormListTile({
    super.key,
    required this.iconPath,
    this.iconColor,
    required this.onChanged,
    required this.hintText,
    required this.subtitle,
    this.margin,
    this.padding,
    required this.color,
  });

  @override
  State<IaIventCreateFormListTile> createState() => _IaIventCreateFormListTileState();

  // ============================================================================
  // FACTORY CONSTRUCTORS FOR COMMON FORM TYPES
  // ============================================================================

  /// Creates a Google Forms URL input tile
  static IaIventCreateFormListTile googleFormsUrlTile({
    required Function(String) onChanged,
  }) {
    return IaIventCreateFormListTile(
      margin: const EdgeInsets.only(top: AppDimensions.padding20),
      onChanged: onChanged,
      hintText: 'Form Linkini Yazınız',
      subtitle: 'Google Forms',
      color: AppColors.purpleLight,
      iconPath: AppAssets.googleForms,
      iconColor: AppColors.purple,
    );
  }

  /// Creates an Instagram username input tile
  static IaIventCreateFormListTile instagramUsernameTile({
    required Function(String) onChanged,
  }) {
    return IaIventCreateFormListTile(
      margin: const EdgeInsets.only(top: AppDimensions.padding20),
      onChanged: onChanged,
      hintText: '@kullaniciadi',
      subtitle: 'Instagram DM',
      color: AppColors.instagram,
      iconPath: AppAssets.instagram,
    );
  }

  /// Creates a WhatsApp group URL input tile
  static IaIventCreateFormListTile whatsappUrlTile({
    required Function(String) onChanged,
  }) {
    return IaIventCreateFormListTile(
      margin: const EdgeInsets.only(top: AppDimensions.padding20),
      onChanged: onChanged,
      hintText: 'Grup Linkini Yazınız',
      subtitle: 'WhatsApp Grubu',
      color: AppColors.whatsappGreen,
      iconPath: AppAssets.whatsapp,
    );
  }

  /// Creates a WhatsApp phone number input tile
  static IaIventCreateFormListTile whatsappNumberTile({
    required Function(String) onChanged,
  }) {
    return IaIventCreateFormListTile(
      margin: const EdgeInsets.only(top: AppDimensions.padding20),
      onChanged: onChanged,
      hintText: 'Telefon Numaranızı Yazınız',
      subtitle: 'WhatsApp Mesajı',
      color: AppColors.whatsappGreen,
      iconPath: AppAssets.whatsapp,
    );
  }

  /// Creates a phone call number input tile
  static IaIventCreateFormListTile callNumberTile({
    required Function(String) onChanged,
  }) {
    return IaIventCreateFormListTile(
      margin: const EdgeInsets.only(top: AppDimensions.padding20),
      onChanged: onChanged,
      hintText: 'Telefon Numaranızı Yazınız',
      subtitle: 'Sesli Arama',
      color: AppColors.primary,
      iconPath: AppAssets.phone,
    );
  }

  /// Creates a website URL input tile
  static IaIventCreateFormListTile websiteUrlTile({
    required Function(String) onChanged,
  }) {
    return IaIventCreateFormListTile(
      margin: const EdgeInsets.only(top: AppDimensions.padding20),
      onChanged: onChanged,
      hintText: 'Etkinlik Linkini Yazınız',
      subtitle: 'Bilet / Katılım Bağlantısı',
      color: AppColors.primary,
      iconPath: AppAssets.link,
    );
  }
}

/// Private state class for the form list tile widget
class _IaIventCreateFormListTileState extends State<IaIventCreateFormListTile> {
  /// Text controller for the input field
  final TextEditingController _textEditingController = TextEditingController();

  @override
  void dispose() {
    _textEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: widget.margin,
      padding: widget.padding,
      color: AppColors.transparent,
      child: Row(
        children: [
          _buildIconCircle(),
          const SizedBox(width: AppDimensions.padding12),
          Expanded(child: _buildInputSection()),
          _buildCheckboxIndicator(),
        ],
      ),
    );
  }

  /// Builds the circular icon container
  Widget _buildIconCircle() {
    return CircleAvatar(
      radius: 24,
      backgroundColor: widget.color,
      child: IaSvgIcon(
        iconPath: widget.iconPath,
        iconColor: widget.iconColor,
      ),
    );
  }

  /// Builds the input field and subtitle section
  Widget _buildInputSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTextField(),
        Text(widget.subtitle, style: AppTextStyles.size12Regular),
      ],
    );
  }

  /// Builds the text input field
  Widget _buildTextField() {
    return Container(
      height: 18,
      child: TextField(
        controller: _textEditingController,
        onChanged: (String val) => setState(() => widget.onChanged(val)),
        style: AppTextStyles.size16MediumPrimary,
        decoration: InputDecoration(
          hintText: widget.hintText,
          hintStyle: AppTextStyles.size16MediumTextSecondary,
          border: InputBorder.none,
        ),
      ),
    );
  }

  /// Builds the checkbox indicator showing if field is filled
  Widget _buildCheckboxIndicator() {
    return IaCheckbox(
      isSelected: _textEditingController.text.isNotEmpty,
    );
  }
}

import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/side_menu/constants/drawer_constants.dart';
import 'package:ivent_app/features/side_menu/utils/profile_drawer_dialogs.dart';
import 'package:ivent_app/features/side_menu/widgets/common/profile_drawer_action_buttons.dart';
import 'package:ivent_app/features/side_menu/widgets/common/profile_drawer_header.dart';
import 'package:ivent_app/features/side_menu/widgets/common/profile_drawer_level_info.dart';
import 'package:ivent_app/features/side_menu/widgets/common/profile_drawer_pages_section.dart';

class IaProfileSimpleDrawer extends StatelessWidget {
  const IaProfileSimpleDrawer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: AppColors.background,
      child: SafeArea(
        child: Column(
          children: [
            const ProfileDrawerHeader(),
            const ProfileDrawerLevelInfo(),
            const SizedBox(height: AppDimensions.padding16),
            const ProfileDrawerActionButtons(),
            const SizedBox(height: AppDimensions.padding20),
            const ProfileDrawerPagesSection(),
            const Spacer(),
            Container(
              padding: const EdgeInsets.all(AppDimensions.padding20),
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: AppColors.lightGrey, width: 1),
                ),
              ),
              child: Column(
                children: [
                  _buildDrawerItem(
                    icon: AppAssets.closeCircle,
                    title: DrawerConstants.deleteAccountText,
                    titleColor: AppColors.error,
                    iconColor: AppColors.error,
                    onTap: () {
                      ProfileDrawerDialogs.showDeleteAccountDialog(context);
                    },
                  ),
                  const SizedBox(height: AppDimensions.padding8),
                  _buildDrawerItem(
                    icon: AppAssets.closeLG,
                    title: DrawerConstants.logoutText,
                    titleColor: AppColors.error,
                    iconColor: AppColors.error,
                    onTap: () {
                      ProfileDrawerDialogs.showLogoutDialog(context);
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerItem({
    required String icon,
    required String title,
    required VoidCallback onTap,
    Color? titleColor,
    Color? iconColor,
  }) {
    return ListTile(
      leading: IaSvgIcon(
        iconPath: icon,
        iconSize: 24,
        iconColor: iconColor ?? AppColors.darkGrey,
      ),
      title: Text(
        title,
        style: AppTextStyles.size14Medium.copyWith(
          color: titleColor ?? AppColors.textPrimary,
        ),
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.padding20,
        vertical: AppDimensions.padding4,
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/side_menu/constants/profile_constants.dart';
import 'package:ivent_app/features/profile/controllers/profile_shared_state.dart';
import 'package:ivent_app/features/side_menu/widgets/common/profile_error_state.dart';
import 'package:ivent_app/features/side_menu/widgets/level_steps/congratulations_section.dart';
import 'package:ivent_app/features/side_menu/widgets/level_steps/level_step_item.dart';
import 'package:ivent_app/features/side_menu/widgets/level_steps/level_steps_header.dart';
import 'package:ivent_app/features/side_menu/controllers/profile_level_steps_controller.dart';

class ProfileLevelStepsPage extends StatefulWidget {
  const ProfileLevelStepsPage({super.key});

  @override
  State<ProfileLevelStepsPage> createState() => _ProfileLevelStepsPageState();
}

class _ProfileLevelStepsPageState extends State<ProfileLevelStepsPage> {
  late final ProfileLevelStepsController _controller;

  @override
  void initState() {
    super.initState();
    _controller = Get.put(ProfileLevelStepsController(Get.find(), Get.find<ProfileSharedState>()));
  }

  @override
  void dispose() {
    Get.delete<ProfileLevelStepsController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      title: ProfileConstants.levelStepsTitle,
      body: Obx(() {
        if (_controller.isLoading()) {
          return const Center(child: CircularProgressIndicator());
        }

        if (_controller.hasError) {
          return ProfileErrorState(
            errorMessage: _controller.errorMessage,
            onRetry: () => _controller.initController(),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(AppDimensions.padding16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              LevelStepsHeader(
                title: _controller.levelTitle,
                description: _controller.levelDescription,
              ),
              const SizedBox(height: AppDimensions.padding24),
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _controller.steps.length,
                separatorBuilder: (context, index) => const SizedBox(height: AppDimensions.padding12),
                itemBuilder: (context, index) {
                  final step = _controller.steps[index];
                  return LevelStepItem(
                    step: step,
                    index: index,
                    onStepAction: _controller.onStepAction,
                  );
                },
              ),
              const SizedBox(height: AppDimensions.padding24),
              if (_controller.isAllStepsCompleted)
                const CongratulationsSection()
              else
                GestureDetector(
                  onTap: _controller.onNextStep,
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(AppDimensions.padding16),
                    decoration: BoxDecoration(
                      color: _controller.canProceedToNextStep ? AppColors.primary : AppColors.grey300,
                      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                    ),
                    child: Center(
                      child: Text(
                        _controller.nextStepButtonText,
                        style: AppTextStyles.size16Bold.copyWith(
                          color: AppColors.white,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      }),
    );
  }
}

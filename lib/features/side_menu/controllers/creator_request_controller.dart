import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/controllers/shared_state.dart';

/// Controller for managing Creator request multi-step process
///
/// Handles the complete Creator application flow including:
/// - Step 1: Introduction and benefits
/// - Step 2: Personal information and experience
/// - Step 3: Verification and social media
/// - Step 4: Application summary and submission
/// - Status tracking
class CreatorRequestController extends BaseController<SharedState> {
  // Debug flag to bypass level requirements in debug mode
  static const bool _bypassLevelRequirementsInDebug = true;

  // State variables
  final _hasError = false.obs;
  final _errorMessage = ''.obs;
  final _currentStep = 1.obs;
  final _isApplicationSubmitted = false.obs;
  final _userLevel = Rxn<GetLevelByUserIdReturn>();
  final _levelRequirements = <String, bool>{}.obs;
  final description = ''.obs;

  // Step 2: Personal Information
  final _fullName = ''.obs;
  final _email = ''.obs;
  final _phone = ''.obs;
  final _birthDate = Rxn<DateTime>();
  final _city = ''.obs;
  final _bio = ''.obs;
  final _experience = ''.obs;
  final _whyCreator = ''.obs;

  // Step 3: Verification
  final _identityNumber = ''.obs;
  final _instagramHandle = ''.obs;
  final _twitterHandle = ''.obs;
  final _linkedinHandle = ''.obs;
  final _websiteUrl = ''.obs;
  final _portfolioUrl = ''.obs;

  // Text Controllers for forms
  late final TextEditingController descriptionController;
  late final TextEditingController fullNameController;
  late final TextEditingController emailController;
  late final TextEditingController phoneController;
  late final TextEditingController birthDateController;
  late final TextEditingController cityController;
  late final TextEditingController bioController;
  late final TextEditingController experienceController;
  late final TextEditingController motivationController;
  late final TextEditingController tcNoController;
  late final TextEditingController instagramController;
  late final TextEditingController twitterController;
  late final TextEditingController tiktokController;
  late final TextEditingController youtubeController;
  late final TextEditingController websiteController;
  late final TextEditingController additionalLinksController;

  // Constructor
  CreatorRequestController(AuthService authService, SharedState state) : super(authService, state) {
    _initializeControllers();
  }

  // Getters
  bool get hasError => _hasError.value;
  String get errorMessage => _errorMessage.value;
  int get currentStep => _currentStep.value;
  bool get isApplicationSubmitted => _isApplicationSubmitted.value;
  GetLevelByUserIdReturn? get userLevel => _userLevel.value;
  Map<String, bool> get levelRequirements => _levelRequirements;

  // Step 2 Getters
  String get fullName => _fullName.value;
  String get email => _email.value;
  String get phone => _phone.value;
  DateTime? get birthDate => _birthDate.value;
  String get city => _city.value;
  String get bio => _bio.value;
  String get experience => _experience.value;
  String get whyCreator => _whyCreator.value;

  // Step 3 Getters
  String get identityNumber => _identityNumber.value;
  String get instagramHandle => _instagramHandle.value;
  String get twitterHandle => _twitterHandle.value;
  String get linkedinHandle => _linkedinHandle.value;
  String get websiteUrl => _websiteUrl.value;
  String get portfolioUrl => _portfolioUrl.value;

  // Step 2 Setters
  set fullName(String value) => _fullName.value = value;
  set email(String value) => _email.value = value;
  set phone(String value) => _phone.value = value;
  set birthDate(DateTime? value) => _birthDate.value = value;
  set city(String value) => _city.value = value;
  set bio(String value) => _bio.value = value;
  set experience(String value) => _experience.value = value;
  set whyCreator(String value) => _whyCreator.value = value;

  // Step 3 Setters
  set identityNumber(String value) => _identityNumber.value = value;
  set instagramHandle(String value) => _instagramHandle.value = value;
  set twitterHandle(String value) => _twitterHandle.value = value;
  set linkedinHandle(String value) => _linkedinHandle.value = value;
  set websiteUrl(String value) => _websiteUrl.value = value;
  set portfolioUrl(String value) => _portfolioUrl.value = value;

  // Creator benefits and requirements
  List<String> get creatorBenefits => [
        'Özel Creator rozeti ve profil işareti',
        'Sınırsız etkinlik oluşturma',
        'Gelişmiş analitik raporlar',
        'Öncelikli müşteri desteği',
        'Özel Creator etkinlikleri ve ağ kurma',
        'Gelir paylaşım programına erişim',
        'Beta özelliklerini ilk deneme fırsatı',
      ];

  // Dynamic requirements based on user level (DEBUG MODE ONLY)
  Map<String, String> get requirementDescriptions => {
        if (kDebugMode) ...{
          'account_complete': 'Tam Hesap olmak',
          'bilincli_hesap': 'Bilinçli Hesap olmak',
          'level_requirement': '10 Arkadaşını davet et. (${_getCurrentLevelText()})',
        }
      };

  String _getCurrentLevelText() {
    if (!kDebugMode) return '10/10'; // Production'da her zaman tamamlanmış göster
    if (userLevel?.levelInfo == null) return '0/10';
    final level = userLevel!.levelInfo;
    switch (level) {
      case UserRoleEnum.level0:
        return '0/10';
      case UserRoleEnum.level1:
        return '1/10';
      case UserRoleEnum.level2:
        return '2/10';
      case UserRoleEnum.level3:
        return '3/10';
      case UserRoleEnum.level4:
        return '4/10';
      case UserRoleEnum.level5:
        return '5/10';
      case UserRoleEnum.level6:
        return '6/10';
      case UserRoleEnum.creator:
        return '10/10';
      default:
        return '0/10';
    }
  }

  // Check if all requirements are met (DEBUG MODE ONLY)
  bool get allRequirementsMet {
    if (!kDebugMode) return true; // Production'da her zaman true

    // Debug modda level kontrollerini bypass et
    if (_bypassLevelRequirementsInDebug) {
      debugPrint('🔧 [DEBUG] Bypassing level requirements check');
      return true;
    }

    return levelRequirements.values.every((requirement) => requirement == true);
  }

  // Individual requirement getters for UI (DEBUG MODE ONLY)
  bool get isCompleteAccount {
    if (!kDebugMode) return true; // Production'da her zaman true
    return levelRequirements['account_complete'] == true;
  }

  bool get isConsciousAccount {
    if (!kDebugMode) return true; // Production'da her zaman true
    return levelRequirements['bilincli_hesap'] == true;
  }

  bool get hasInvitedFriends {
    if (!kDebugMode) return true; // Production'da her zaman true
    return levelRequirements['level_requirement'] == true;
  }

  int get invitedFriendsCount {
    if (!kDebugMode) return 10; // Production'da her zaman 10
    // This would come from API in real implementation
    // For now, return 0 as we don't have friend invitation count
    return 0;
  }

  String get applicationStatus {
    if (!authService.isLoggedIn) {
      return 'Oturum açmanız gerekiyor.';
    }
    if (isApplicationSubmitted) {
      return 'Başvurunuz başarıyla gönderildi! En kısa sürede değerlendirip size dönüş yapacağız.';
    } else if (sessionUser.sessionRole == UserRoleEnum.creator) {
      return 'Zaten bir iVent Creator\'sınız!';
    } else {
      return 'iVent Creator olmak için başvurunuzu tamamlayın.';
    }
  }

  bool get canApply {
    if (!kDebugMode) {
      // Production'da sadece temel kontroller
      return authService.isLoggedIn && sessionUser.sessionRole != UserRoleEnum.creator && !isApplicationSubmitted;
    }

    // Debug modda level kontrollerini bypass et
    if (_bypassLevelRequirementsInDebug) {
      debugPrint('🔧 [DEBUG] Bypassing level requirements in debug mode');
      return authService.isLoggedIn && sessionUser.sessionRole != UserRoleEnum.creator && !isApplicationSubmitted;
    }

    // Debug modda level kontrolü de yap (normal debug behavior)
    return authService.isLoggedIn &&
        sessionUser.sessionRole != UserRoleEnum.creator &&
        !isApplicationSubmitted &&
        allRequirementsMet;
  }

  // Step validation
  bool get isStep1Valid => true; // Introduction step is always valid

  bool get isStep2Valid {
    return description.value.trim().length >= 140;
  }

  bool get isStep3Valid {
    return email.trim().isNotEmpty && GetUtils.isEmail(email.trim()) && city.trim().isNotEmpty;
  }

  // Progress calculation
  double get progressPercentage {
    switch (currentStep) {
      case 1:
        return 0.33; // Step 1: Requirements check
      case 2:
        return 0.66; // Step 2: Description/Motivation
      case 3:
        return 1.0; // Step 3: Personal info (final)
      default:
        return 0.0;
    }
  }

  // Lifecycle
  @override
  Future<void> initController() async {
    super.initController();
    _checkExistingApplication();
    _loadUserData();
    if (kDebugMode) {
      _loadUserLevel(); // Sadece debug modda level yükle
    }
    debugPrint('CreatorRequestController initialized for user: ${sessionUser.sessionId}');
  }

  // Initialize text controllers
  void _initializeControllers() {
    descriptionController = TextEditingController();
    fullNameController = TextEditingController();
    emailController = TextEditingController();
    phoneController = TextEditingController();
    birthDateController = TextEditingController();
    cityController = TextEditingController();
    bioController = TextEditingController();
    experienceController = TextEditingController();
    motivationController = TextEditingController();
    tcNoController = TextEditingController();
    instagramController = TextEditingController();
    twitterController = TextEditingController();
    tiktokController = TextEditingController();
    youtubeController = TextEditingController();
    websiteController = TextEditingController();
    additionalLinksController = TextEditingController();
  }

  // Navigation methods
  void goToStep(int step) {
    if (step >= 1 && step <= 3) {
      _currentStep.value = step;
    }
  }

  void nextStep() {
    if (currentStep < 3) {
      _currentStep.value = currentStep + 1;
    }
  }

  void previousStep() {
    if (currentStep > 1) {
      _currentStep.value = currentStep - 1;
    }
  }

  // Step navigation methods
  void goToStep2() {
    if (kDebugMode && !_bypassLevelRequirementsInDebug && !allRequirementsMet) {
      _showError('Debug modda: Tüm şartları tamamlayın.');
      return;
    }
    Get.toNamed('/creator_request_step2');
  }

  void goToStep3() {
    if (description.value.length >= 140) {
      Get.toNamed('/creator_request_step3');
    }
  }

  // Step validation methods
  bool validateCurrentStep() {
    switch (currentStep) {
      case 1:
        return isStep1Valid;
      case 2:
        return isStep2Valid;
      case 3:
        return isStep3Valid;
      case 4:
        return true; // Summary step
      default:
        return false;
    }
  }

  String getStepValidationMessage() {
    switch (currentStep) {
      case 2:
        if (!isStep2Valid) {
          return 'Lütfen tüm zorunlu alanları doldurun.';
        }
        break;
      case 3:
        if (!isStep3Valid) {
          return 'Lütfen kimlik numaranızı ve Instagram hesabınızı girin.';
        }
        break;
    }
    return '';
  }

  // Step-specific validation methods
  bool validateStep2() {
    return fullNameController.text.trim().isNotEmpty &&
        emailController.text.trim().isNotEmpty &&
        phoneController.text.trim().isNotEmpty &&
        birthDateController.text.trim().isNotEmpty &&
        cityController.text.trim().isNotEmpty &&
        bioController.text.trim().isNotEmpty &&
        experienceController.text.trim().isNotEmpty &&
        motivationController.text.trim().isNotEmpty;
  }

  bool validateStep3() {
    return tcNoController.text.trim().isNotEmpty &&
        (instagramController.text.trim().isNotEmpty ||
            twitterController.text.trim().isNotEmpty ||
            tiktokController.text.trim().isNotEmpty ||
            youtubeController.text.trim().isNotEmpty);
  }

  // Step navigation helpers
  bool get canProceedToStep2 => !kDebugMode || _bypassLevelRequirementsInDebug || allRequirementsMet;
  bool get canProceedToStep3 => validateStep2();
  bool get canProceedToStep4 => validateStep3();

  // Dialog methods
  void showBenefitsDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Creator Avantajları'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: creatorBenefits
                .map((benefit) => Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Icon(Icons.check_circle, color: Colors.green, size: 16),
                          const SizedBox(width: 8),
                          Expanded(child: Text(benefit)),
                        ],
                      ),
                    ))
                .toList(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Kapat'),
          ),
        ],
      ),
    );
  }

  void showRequirementsDialog() {
    if (!kDebugMode) return; // Production'da requirements dialog gösterme

    Get.dialog(
      AlertDialog(
        title: const Text('Başvuru Şartları (Debug Mode)'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: requirementDescriptions.entries
                .map((entry) => Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Icon(
                            levelRequirements[entry.key] == true ? Icons.check_circle : Icons.radio_button_unchecked,
                            color: levelRequirements[entry.key] == true ? Colors.green : Colors.grey,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Expanded(child: Text(entry.value)),
                        ],
                      ),
                    ))
                .toList(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Kapat'),
          ),
        ],
      ),
    );
  }

  // Submit method for step 4
  Future<void> submitCreatorRequest() async {
    await sendCreatorRequest();
  }

  // Public methods

  /// Sends creator request application
  Future<void> sendCreatorRequest() async {
    debugPrint('🔍 [DEBUG] sendCreatorRequest called');
    debugPrint('🔍 [DEBUG] canApply: $canApply');
    debugPrint('🔍 [DEBUG] authService.isLoggedIn: ${authService.isLoggedIn}');
    debugPrint('🔍 [DEBUG] sessionUser.sessionRole: ${sessionUser.sessionRole}');
    debugPrint('🔍 [DEBUG] isApplicationSubmitted: $isApplicationSubmitted');

    if (kDebugMode) {
      debugPrint('🔍 [DEBUG] allRequirementsMet: $allRequirementsMet');
      debugPrint('🔍 [DEBUG] levelRequirements: $levelRequirements');
      debugPrint('🔍 [DEBUG] userLevel: ${userLevel?.levelInfo}');
      debugPrint('🔍 [DEBUG] isCompleteAccount: $isCompleteAccount');
      debugPrint('🔍 [DEBUG] isConsciousAccount: $isConsciousAccount');
      debugPrint('🔍 [DEBUG] hasInvitedFriends: $hasInvitedFriends');
    }

    if (!canApply) {
      debugPrint('❌ [DEBUG] canApply is false, showing error');
      _showError('Bu işlemi gerçekleştiremezsiniz.');
      return;
    }

    debugPrint('🔍 [DEBUG] isStep2Valid: $isStep2Valid');
    debugPrint('🔍 [DEBUG] description.length: ${description.value.length}');
    debugPrint('🔍 [DEBUG] isStep3Valid: $isStep3Valid');
    debugPrint('🔍 [DEBUG] email: "$email"');
    debugPrint('🔍 [DEBUG] city: "$city"');

    if (!isStep2Valid || !isStep3Valid) {
      _showError('Lütfen tüm zorunlu alanları doldurun.');
      return;
    }

    try {
      // Send creator request using the API - Backend will handle level requirements
      await usersApi.sendCreatorRequestForm(sessionUser.sessionId);

      _isApplicationSubmitted.value = true;

      Get.snackbar(
        'Başarılı!',
        'Creator başvurunuz başarıyla gönderildi. En kısa sürede değerlendirip size dönüş yapacağız.',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 4),
      );

      // Navigate to status page
      Get.toNamed('/creator_request_status');
    } catch (e) {
      _handleApiError(e, 'Başvuru gönderilirken bir hata oluştu.');
    } finally {}
  }

  /// Loads user data to pre-fill form
  void _loadUserData() {
    if (authService.isLoggedIn) {
      // Pre-fill with existing user data if available
      fullName = sessionUser.sessionFullname;
      fullNameController.text = sessionUser.sessionFullname;
      // Email will be loaded from user profile API if needed
      // Other fields can be loaded from user profile if available
    }
  }

  /// Loads user level and updates requirements (DEBUG MODE ONLY)
  Future<void> _loadUserLevel() async {
    if (!kDebugMode || !authService.isLoggedIn) return;

    debugPrint('🔍 [DEBUG] Loading user level for debug mode...');

    try {
      // Get user level from API
      final levelData = await usersApi.getLevelByUserId(sessionUser.sessionId);
      _userLevel.value = levelData;

      debugPrint('🔍 [DEBUG] Level data loaded: ${levelData?.levelInfo}');

      // Update requirements based on level
      _updateRequirements();
    } catch (e) {
      debugPrint('❌ [DEBUG] Failed to load user level: $e');
      // Set default requirements if API fails
      _setDefaultRequirements();
    } finally {}
  }

  /// Updates requirements based on user level (DEBUG MODE ONLY)
  void _updateRequirements() {
    if (!kDebugMode) return;

    debugPrint('🔍 [DEBUG] Updating requirements...');

    if (userLevel?.levelInfo == null) {
      debugPrint('❌ [DEBUG] User level is null, setting default requirements');
      _setDefaultRequirements();
      return;
    }

    final level = userLevel!.levelInfo;
    debugPrint('🔍 [DEBUG] Current user level: $level');

    // Check account completion (tam hesap)
    final isAccountComplete = _isAccountComplete(level);
    _levelRequirements['account_complete'] = isAccountComplete;
    debugPrint('🔍 [DEBUG] Account complete: $isAccountComplete');

    // Check bilinçli hesap (level 1+)
    final isBilincliHesap = _isBilincliHesap(level);
    _levelRequirements['bilincli_hesap'] = isBilincliHesap;
    debugPrint('🔍 [DEBUG] Bilinçli hesap: $isBilincliHesap');

    // Check friend invitation requirement (level progression)
    final isLevelRequirement = _isLevelRequirementMet(level);
    _levelRequirements['level_requirement'] = isLevelRequirement;
    debugPrint('🔍 [DEBUG] Level requirement: $isLevelRequirement');

    debugPrint('🔍 [DEBUG] Final requirements: $_levelRequirements');
  }

  /// Sets default requirements when API fails (DEBUG MODE ONLY)
  void _setDefaultRequirements() {
    if (!kDebugMode) return;

    debugPrint('🔍 [DEBUG] Setting default requirements (all false)');

    _levelRequirements['account_complete'] = false;
    _levelRequirements['bilincli_hesap'] = false;
    _levelRequirements['level_requirement'] = false;
  }

  /// Checks if account is complete (Level 5+ or Creator) (DEBUG MODE ONLY)
  bool _isAccountComplete(UserRoleEnum level) {
    return level == UserRoleEnum.level5 || level == UserRoleEnum.level6 || level == UserRoleEnum.creator;
  }

  /// Checks if user has bilinçli hesap (Level 1+) (DEBUG MODE ONLY)
  bool _isBilincliHesap(UserRoleEnum level) {
    return level != UserRoleEnum.level0;
  }

  /// Checks if level requirement is met (Creator level) (DEBUG MODE ONLY)
  bool _isLevelRequirementMet(UserRoleEnum level) {
    return level == UserRoleEnum.creator;
  }

  @override
  void onClose() {
    // Dispose text controllers
    descriptionController.dispose();
    fullNameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    birthDateController.dispose();
    cityController.dispose();
    bioController.dispose();
    experienceController.dispose();
    motivationController.dispose();
    tcNoController.dispose();
    instagramController.dispose();
    twitterController.dispose();
    tiktokController.dispose();
    youtubeController.dispose();
    websiteController.dispose();
    additionalLinksController.dispose();
    super.onClose();
  }

  /// Checks if user already has a pending application
  void _checkExistingApplication() {
    if (sessionUser.sessionRole == UserRoleEnum.creator) {
      _isApplicationSubmitted.value = true;
    }
  }

  /// Handles API errors
  void _handleApiError(dynamic error, String defaultMessage) {
    String message = defaultMessage;

    if (error is ApiException) {
      switch (error.code) {
        case 400:
          message = 'Başvuru bilgilerinizde bir hata var. Lütfen kontrol edin.';
          break;
        case 401:
          message = 'Oturum açmanız gerekiyor.';
          break;
        case 403:
          message = 'Bu işlemi gerçekleştirme yetkiniz yok.';
          break;
        case 409:
          message = 'Zaten bir başvurunuz bulunmaktadır.';
          break;
        default:
          message = defaultMessage;
      }
    }

    _showError(message);
    debugPrint('CreatorRequestController API Error: $error');
  }

  /// Shows error message
  void _showError(String message) {
    _hasError.value = true;
    _errorMessage.value = message;

    Get.snackbar(
      'Hata',
      message,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
    );
  }

  /// Clears error state
  void clearError() {
    _hasError.value = false;
    _errorMessage.value = '';
  }
}

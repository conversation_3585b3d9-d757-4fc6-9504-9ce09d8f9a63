import 'package:flutter/material.dart';
import 'package:ivent_app/core/widgets/index.dart';

class IventThumbnail extends StatelessWidget {
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final Widget body;
  final List<Widget> buttons;
  final Gradient gradient;
  final double? roundness;
  final String? thumbnailUrl;

  const IventThumbnail({
    super.key,
    this.margin = const EdgeInsets.all(0),
    this.padding = const EdgeInsets.all(0),
    required this.body,
    required this.buttons,
    required this.gradient,
    this.roundness,
    this.thumbnailUrl,
  });

  @override
  Widget build(BuildContext context) {
    return IaRoundedContainer(
      margin: margin,
      padding: padding,
      roundness: roundness,
      child: Stack(
        children: [
          IaImageContainer.withImageUrl(imageUrl: thumbnailUrl, roundness: roundness),
          IaRoundedContainer(
            gradient: gradient,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                body,
                const Spacer(),
                Row(mainAxisAlignment: MainAxisAlignment.end, children: buttons),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

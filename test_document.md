# Main Document Title

This is some introductory content that appears before any second-level headers.
It should be saved as INTRO.md.

## Getting Started

This section explains how to get started with the project.

### Installation Steps

1. Clone the repository
2. Install dependencies
3. Run the application

## Configuration & Setup

This section covers configuration and setup procedures.

The configuration involves several steps:

- Database setup
- Environment variables
- API keys

## API Reference

Here's the API documentation.

### Authentication

All API calls require authentication.

### Endpoints

- GET /api/users
- POST /api/users
- PUT /api/users/{id}
- DELETE /api/users/{id}

## Troubleshooting & FAQ

Common issues and their solutions.

**Q: Why isn't my app starting?**
A: Check your configuration files.

**Q: How do I reset my password?**
A: Use the password reset feature.

## Special Characters & Edge Cases!

This header contains special characters that should be handled properly.

Some content with _markdown_ formatting and `code blocks`.

```python
def example():
    return "Hello World"
```

##

This is an edge case with an empty header (just ##).

## Final Notes

Last section with some final thoughts and conclusions.

- Thank you for reading
- Please provide feedback
- Visit our website for more info

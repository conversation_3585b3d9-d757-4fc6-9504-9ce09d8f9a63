{   
    "files.exclude": {
      "**/__pycache__": true,
      "**/.venv": true,
      "**/tools": true,
      "**/depreciated": true,
      ".dart_tool": true,
      // "android": true,
      // "build": true,
      "ios": true,
      "linux": true,
      "macos": true,
      "web": true,
      "windows": true,
      ".flutter-plugins": true,
      ".flutter-plugins-dependencies": true,
      ".metadata": true,
      "analysis_options.yaml": true,
      "devtools_options.yaml": true,
      "pubspec.lock": true,

      // "api-json.json": true,
      "swagger_to_dart.py": true,
      "converter.py": true
    },
    "dart.flutterSdkPath": null,
    "dart.previewFlutterUiGuides": true,
    "dart.previewFlutterUiGuidesCustomTracking": true
}
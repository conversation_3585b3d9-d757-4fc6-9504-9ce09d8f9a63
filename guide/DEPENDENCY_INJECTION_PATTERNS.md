## Dependency Injection Patterns

### Route Bindings

```dart
class FeatureBindings implements Bindings {
  @override
  void dependencies() {
    if (Get.isRegistered<FeatureController>()) return;

    Get.lazyPut(() => FeatureSharedState(), fenix: true);
    Get.lazyPut<FeatureController>(
      () => FeatureController(
        Get.find<AuthService>(),
        Get.find<FeatureSharedState>(),
      ),
      fenix: true,
    );
  }
}
```

### Binding Rules

- Use `lazyPut()` for lazy initialization
- Set `fenix: true` for persistent instances
- Check registration to avoid duplicates
- Inject dependencies through constructor

## Controller Architecture

### Base Controller Pattern

All controllers must extend `BaseController<T extends SharedState>` for consistent behavior and shared functionality:

```dart
class FeatureController extends BaseController<FeatureSharedState> {
  // Child controllers - declared as late final for initialization in initController
  late final SubController1 subController1;
  late final SubController2 subController2;
  late final SubController3 subController3;

  // Constructor - always pass AuthService and SharedState
  FeatureController(AuthService authService, FeatureSharedState state)
    : super(authService, state);

  @override
  Future<void> initController() async {
    super.initController();

    // Initialize child controllers in dependency order
    // Controllers with no dependencies first
    subController1 = Get.put(SubController1(authService, state));
    subController2 = Get.put(SubController2(authService, state));

    // Controllers with dependencies on other controllers last
    subController3 = Get.put(SubController3(authService, state, subController1));

    // Perform any async initialization
    await _loadInitialData();
  }

  @override
  void closeController() {
    // Clean up child controllers in reverse order
    Get.delete<SubController3>();
    Get.delete<SubController2>();
    Get.delete<SubController1>();

    // Call parent cleanup
    super.closeController();
  }

  // Navigation methods - delegate to appropriate sub-controllers
  void goToFeaturePage() => Get.toNamed(FeatureRoutes.FEATURE_PAGE);
  void goToDetailPage(String id) => Get.toNamed(FeatureRoutes.DETAIL_PAGE, arguments: id);

  // Private helper methods
  Future<void> _loadInitialData() async {
    await runSafe(
      () async {
        // Load any required initial data
      },
      loadingTag: 'initData',
    );
  }
}
```

### Sub-Controller Pattern

Sub-controllers handle specific responsibilities within a feature:

```dart
class SubController extends BaseController<FeatureSharedState> {
  // Dependencies from other controllers (if needed)
  final OtherController? otherController;

  // Reactive state specific to this sub-controller
  final _items = <ItemType>[].obs;
  final _selectedItem = Rxn<ItemType>();

  SubController(
    AuthService authService,
    FeatureSharedState state,
    [this.otherController]
  ) : super(authService, state);

  // Getters for reactive state
  List<ItemType> get items => _items;
  ItemType? get selectedItem => _selectedItem.value;

  // Setters for reactive state
  set items(List<ItemType> value) => _items.assignAll(value);
  set selectedItem(ItemType? value) => _selectedItem.value = value;

  @override
  Future<void> initController() async {
    super.initController();
    await loadItems();
  }

  // Business logic methods
  Future<void> loadItems() async {
    await runSafe(
      () async {
        final response = await featureApi.getItems();
        if (response != null) {
          items = response.map((item) => ItemType.fromApi(item)).toList();
        }
      },
      loadingTag: 'loadItems',
    );
  }

  void selectItem(ItemType item) {
    selectedItem = item;
    // Notify other controllers if needed
    otherController?.onItemSelected(item);
  }
}
```

### Controller Hierarchy

1. **Main Controller**: Coordinates feature functionality and manages navigation
2. **Sub-Controllers**: Handle specific responsibilities (data, UI state, business logic)
3. **Shared State**: Manages reactive state accessible across all controllers
4. **Base Controller**: Provides common functionality (loading, error handling, lifecycle)

### Sub-Controller Management Rules

- Initialize sub-controllers in dependency order in `initController()`
- Use `Get.put()` for registration with proper typing
- Clean up with `Get.delete<T>()` in reverse order in `closeController()`
- Pass shared dependencies (authService, state) to all sub-controllers
- Pass controller dependencies as additional constructor parameters
- Use `late final` declarations for child controllers
